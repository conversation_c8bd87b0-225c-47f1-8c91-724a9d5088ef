import { Job } from '@schemas/company/job/job';
import { EntityModule } from '../entity';
import { FastifyStateI } from '@interfaces/common/declaration';
import Ship from '@modules/ship';
import { DesignationModule } from '../designation';
import { DepartmentModule } from '../department';
import { prismaPG } from '@config/db';
import { Prisma } from '@prisma/postgres';
import AppError from '@classes/AppError';
import { pick } from '@utils/data/object';
import AppConfig from '@modules/appConfig';
import { CompanyJobConfigI } from '@interfaces/appConfig/appConfig';
import { addDaysToDateOnly, getDateOnly } from '@utils/data/date';
import { separateCreateAndDeleteData } from '@utils/data/array';
import { isNullUndefined } from '@utils/data/data';
import { EntityMemberModule } from '../entityMember';
import { CursorDataI, IdLabelCountDataTypeI, NumberNullI } from '@interfaces/common/data';
import User from '@modules/user';
import { RouteParamsI } from '@schemas/common/common';
import { JobUtil } from '@utils/modules/company/job/job';
import { NumberUtil } from '@utils/data/number';
import { EntityProfileModule } from '../entityProfile';
import { createIndex, deleteDocument, insertDocument, updateDocument } from '@utils/search/elasticsearch';
import { JobFiltersParamsI, PostedJobsFiltersParamsI } from '@interfaces/company/job';
import { JobE } from '@consts/company/job/job';
import { ApplicationE } from '@consts/company/job/application';

export const JobModule = {
  fetchManyForCandidate: async (
    state: FastifyStateI,
    { cursorId, entity, isOfficial, pageSize, status }: Job.FetchManyForCandidateQueryI,
    params: Job.FetchManyForCandidateBodyI,
  ): Promise<CursorDataI<Job.SearchForCandidateResultI>> => {
    const selfProfileId = state.profileId;
    const where: Prisma.JobWhereInput = {
      isOfficial,
    };
    if (isOfficial) {
      if (!entity) {
        throw new AppError('JOB012');
      }
      await EntityMemberModule.isAnyMember({ entity, profileId: selfProfileId });
    } else {
      where.profileId = selfProfileId;
    }
    if (entity) {
      if (entity.dataType === 'master') {
        where.entityId = entity.id;
      } else {
        where.entityRawDataId = entity.id;
      }
    }
    if (status) {
      where.status = status;
    }
    if (cursorId) {
      where.cursorId = {
        lt: BigInt(cursorId),
      };
    }
    const result = await JobModule.searchForCandidate(state, {
      cursorId,
      entity,
      isOfficial,
      pageSize,
      status,
      designations: params?.designations ? params.designations : [],
      countries: params?.countries ? params.countries : [],
      shipTypes: params?.shipTypes ? params.shipTypes : [],
      internetLimits: params?.internetLimits ? params.internetLimits : []
    });
    return result;
  },
  fetchManyForEntityMember: async (
    state: FastifyStateI,
    { cursorId, isOfficial, pageSize, status }: Job.FetchManyForEntityMemberQueryI,
    { entity, designations, shipTypes }: Job.FetchManyForEntityMemberBodyI,
  ) => {
    const selfProfileId = state.profileId;
    const where: Prisma.JobWhereInput = {
      isOfficial,
    };
    if (isOfficial) {
      if (!entity) {
        throw new AppError('JOB012');
      }
      await EntityMemberModule.isAnyMember({ entity, profileId: selfProfileId });
    } else {
      where.profileId = selfProfileId;
    }
    if (entity.dataType === 'master') {
      where.entityId = entity.id;
    } else {
      where.entityRawDataId = entity.id;
    }
    if (status) {
      where.status = status;
    }
    if (cursorId) {
      where.cursorId = {
        lt: BigInt(cursorId),
      };
    }
    if (designations?.length) {
      const designationAlternativeIds = [];
      const designationRawDataIds = [];
      designations.map((designation) => {
        if (designation.dataType === 'master') {
          designationAlternativeIds.push(designation.id);
        } else {
          designationRawDataIds.push(designation.id);
        }
      });
      if (designationAlternativeIds.length > 0) {
        where.designationAlternativeId = {
          in: designationAlternativeIds,
        };
      }
      if (designationRawDataIds.length > 0) {
        where.designationRawDataId = {
          in: designationRawDataIds,
        };
      }
    }

    if (shipTypes?.length) {
      where.Ship = {
        mainVesselTypeId: {
          in: shipTypes,
        },
      };
    }
    const jobs = await prismaPG.job.findMany({
      where,
      take: pageSize + 1,
      select: {
        id: true,
        cursorId: true,
        isOfficial: true,
        isUrgent: true,
        expiryDate: true,
        createdAt: true,
        minYears: true,
        maxYears: true,
        minSalary: true,
        maxSalary: true,
        status: true,
        DesignationAlternative: {
          select: {
            id: true,
            name: true,
          },
        },
        DesignationRawData: {
          select: {
            id: true,
            name: true,
          },
        },
        Entity: {
          select: {
            id: true,
            name: true,
          },
        },
        EntityRawData: {
          select: {
            id: true,
            name: true,
          },
        },
        Ship: {
          select: {
            imo: true,
            name: true,
            MainVesselType: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        ShipRawData: {
          select: {
            imo: true,
            name: true,
            MainVesselType: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
    const data: Job.FetchManyForEntityMemberResultI[] = [];

    if (jobs?.length) {
      data.push(
        ...(jobs.slice(0, pageSize) ?? []).map(
          ({
            createdAt,
            cursorId,
            DesignationAlternative,
            DesignationRawData,
            Entity,
            EntityRawData,
            expiryDate,
            id,
            isOfficial,
            isUrgent,
            maxSalary,
            maxYears,
            minSalary,
            minYears,
            status,
          }) =>
            ({
              createdAt,
              cursorId: cursorId.toString(),
              expiryDate,
              id,
              isOfficial,
              isUrgent,
              maxSalary,
              maxYears,
              minSalary,
              minYears,
              status,
              designation: DesignationModule.transformObj({ DesignationAlternative, DesignationRawData }),
              entity: EntityModule.transformObj({ Entity, EntityRawData }),
            }) as unknown as Job.FetchManyForEntityMemberResultI,
        ),
      );
    }
    const nextCursorId: NumberNullI = data.length ? Number(data[data.length - 1].cursorId) : null;
    return {
      data,
      nextCursorId,
    };
  },
  createOne: async (
    state: FastifyStateI,
    {
      department,
      designation,
      entity,
      entityBenefits,
      equipmentCategories,
      equipmentManufacturers,
      equipmentModels,
      expiryDate,
      isOfficial,
      isUrgent,
      maxSalary,
      maxYears,
      minSalary,
      minYears,
      ship,
    }: Job.CreateOneI,
  ) => {
    const selfProfileId = state.profileId;
    const [
      appConfig,
      _entityResult,
      _designationResult,
      _departmentResult,
      _shipResult,
      _equipmentCategoriesResult,
      _equipmentManufacturersResult,
      _equipmentModelsResult,
      _isAnyMemberResult,
      entityProfileResult,
    ] = await Promise.all([
      AppConfig.AppConfigModule.fetchById({ module: 'COMPANY', subModule: 'JOB' }) as Promise<CompanyJobConfigI>,
      EntityModule.fetchById(entity),
      DesignationModule.fetchById(designation),
      DepartmentModule.fetchById(department),
      ship ? Ship.CoreShipModule.fetchByImo(ship) : null,
      equipmentCategories?.length ? Ship.EquipmentCategoryModule.isIdsExists(equipmentCategories) : null,
      equipmentManufacturers?.length ? Ship.EquipmentManufacturerModule.isIdsExists(equipmentManufacturers) : null,
      equipmentModels?.length ? Ship.EquipmentModelModule.isIdsExists(equipmentModels) : null,
      isOfficial ? EntityMemberModule.isAnyMember({ entity, profileId: selfProfileId }) : null,
      isOfficial ? EntityProfileModule.fetchByEntity(entity) : null,
    ]);
    expiryDate = getDateOnly(expiryDate);
    JobModule.isExpiryDateValid({ appConfig, expiryDate, isUrgent });
    const input: Prisma.JobUncheckedCreateInput = {
      expiryDate,
      profileId: selfProfileId,
      minSalary,
      minYears,
    };
    if (isOfficial) {
      input.entityProfileId = entityProfileResult.id;
    }
    if (department?.dataType === 'master') {
      input.departmentAlternativeId = department.id;
    } else {
      input.departmentRawDataId = department.id;
    }
    if (designation?.dataType === 'master') {
      input.designationAlternativeId = designation.id;
    } else {
      input.designationRawDataId = designation.id;
    }
    if (entityBenefits?.length) {
      input.JobEntityBenefit = {
        createMany: {
          data: entityBenefits.map((item) => ({ entityBenefitRawDataId: item.id })),
        },
      };
    }
    if (entity?.dataType === 'master') {
      input.entityId = entity.id;
    } else {
      input.entityRawDataId = entity.id;
    }
    if (ship?.dataType === 'master') {
      input.shipImo = ship.imo;
    } else {
      input.shipRawDataImo = ship.imo;
    }
    if (typeof isUrgent === 'boolean') {
      input.isUrgent = isUrgent;
    }
    if (typeof maxSalary === 'number') {
      input.maxSalary = maxSalary;
    }
    if (typeof maxYears === 'number') {
      input.maxYears = maxYears;
    }
    const result = await prismaPG.job.create({
      data: input,
      include: {
        Entity: true,
        EntityRawData: true,
        DesignationAlternative: true,
        DesignationRawData: true,
        DepartmentAlternative: true,
        DepartmentRawData: true,
        Ship: true,
        ShipRawData: true,
        Profile: {
          select: {
            id: true,
            name: true,
            avatar: true,
            designationText: true,
            entityText: true,
          },
        },
      },
    });
    if (!result) {
      throw new AppError('JOB001');
    }

    try {
      await createIndex('jobs');
      await insertDocument('jobs', {
        id: result.id,
        isOfficial: result.isOfficial,
        isUrgent: result.isUrgent,
        status: result.status,
        expiryDate: result.expiryDate,
        minYears: result.minYears,
        maxYears: result.maxYears,
        minSalary: result.minSalary,
        maxSalary: result.maxSalary,
        createdAt: result.createdAt,
        entityId: result.entityId || result.entityRawDataId,
        entityName: result.Entity?.name || result.EntityRawData?.name || null,
        designationId: result.designationAlternativeId || result.designationRawDataId,
        designationName: result.DesignationAlternative?.name || result.DesignationRawData?.name || null,
        departmentId: result.departmentAlternativeId || result.departmentRawDataId,
        departmentName: result.DepartmentAlternative?.name || result.DepartmentRawData?.name || null,
        shipId: result.shipImo || result.shipRawDataImo,
        shipName: result.Ship?.name || result.ShipRawData?.name || null,
        creatorId: result.profileId,
        creatorName: result.Profile?.name || null,
        creatorAvatar: result.Profile?.avatar || null,
        creatorDesignationText: result.Profile?.designationText || null,
        creatorEntityText: result.Profile?.entityText || null,
      });
    } catch (esError) {
      console.warn('Elasticsearch sync failed:', esError);
    }

    return pick(result, ['id']);
  },
  updateOne: async (
    state: FastifyStateI,
    {
      entityBenefits,
      equipmentCategories,
      equipmentManufacturers,
      equipmentModels,
      expiryDate,
      isUrgent,
      jobId,
      maxYears,
      minYears,
      ship,
    }: Job.UpdateOneI,
  ) => {
    const selfProfileId = state.profileId;
    const select: Prisma.JobSelect = {
      id: true,
      isOfficial: true,
      entityId: true,
      entityRawDataId: true,
      JobEntityBenefit: entityBenefits?.length
        ? {
            select: {
              entityBenefitRawDataId: true,
            },
          }
        : undefined,
      JobEquipmentCategory: equipmentCategories?.length
        ? {
            select: {
              equipmentCategoryId: true,
              equipmentCategoryRawDataId: true,
            },
          }
        : undefined,
      JobEquipmentManufacturer: equipmentManufacturers?.length
        ? {
            select: {
              equipmentManufacturerId: true,
              equipmentManufacturerRawDataId: true,
            },
          }
        : undefined,
      JobEquipmentModel: equipmentModels?.length
        ? {
            select: {
              equipmentModelId: true,
              equipmentModelRawDataId: true,
            },
          }
        : undefined,
    };
    if (typeof isUrgent === 'boolean') {
      select.expiryDate = true;
    }
    if (typeof expiryDate !== 'undefined') {
      select.isUrgent = true;
    }
    if (typeof maxYears === 'boolean') {
      select.maxYears = true;
    }
    if (typeof minYears === 'boolean') {
      select.minYears = true;
    }
    const existingJob = await prismaPG.job.findUnique({
      select,
      where: {
        id: jobId,
      },
    });
    if (!existingJob) {
      throw new AppError('JOB008');
    }
    const [
      appConfig,
      _shipResult,
      _equipmentCategoriesResult,
      _equipmentManufacturersResult,
      _equipmentModelsResult,
      _isAnyMemberResult,
    ] = await Promise.all([
      expiryDate
        ? (AppConfig.AppConfigModule.fetchById({ module: 'COMPANY', subModule: 'JOB' }) as Promise<CompanyJobConfigI>)
        : null,
      ship ? Ship.CoreShipModule.fetchByImo(ship) : null,
      equipmentCategories?.length ? Ship.EquipmentCategoryModule.isIdsExists(equipmentCategories) : null,
      equipmentManufacturers?.length ? Ship.EquipmentManufacturerModule.isIdsExists(equipmentManufacturers) : null,
      equipmentModels?.length ? Ship.EquipmentModelModule.isIdsExists(equipmentModels) : null,
      existingJob.isOfficial
        ? EntityMemberModule.isAnyMember({
            entity: existingJob.entityId
              ? {
                  id: existingJob.entityId,
                  dataType: 'master',
                }
              : {
                  id: existingJob.entityRawDataId,
                  dataType: 'raw',
                },
            profileId: selfProfileId,
          })
        : null,
    ]);

    if ([minYears, maxYears].some((item) => typeof item === 'number')) {
      const minYearsTemp = typeof minYears === 'boolean' ? minYears : existingJob.minYears;
      const maxYearsTemp = typeof maxYears === 'boolean' ? maxYears : existingJob.maxYears;

      if (!(minYearsTemp && isNullUndefined(maxYears))) {
        if (minYearsTemp >= maxYearsTemp) {
          throw new AppError('JOB011');
        }
      }
    }

    if (expiryDate) {
      expiryDate = getDateOnly(expiryDate);
      JobModule.isExpiryDateValid({ appConfig, existingJob, expiryDate, isUrgent });
    }
    const input: Prisma.JobUncheckedUpdateInput = {};
    if (entityBenefits?.length) {
      const { create, remove } = separateCreateAndDeleteData(entityBenefits, existingJob.JobEntityBenefit);

      input.JobEntityBenefit = {
        createMany: {
          data: [
            ...(create?.rawData?.map((entityBenefitRawDataId) => ({
              entityBenefitRawDataId,
            })) || []),
          ],
        },
        deleteMany: {
          ...(remove?.rawData?.length
            ? {
                entityBenefitRawDataId: {
                  in: remove.rawData,
                },
              }
            : {}),
        },
      };
    }

    if (equipmentManufacturers?.length) {
      const { create, remove } = separateCreateAndDeleteData(
        equipmentManufacturers,
        existingJob.JobEquipmentManufacturer,
      );

      input.JobEquipmentManufacturer = {
        createMany: {
          data: [
            ...(create?.master?.map((equipmentManufacturerId) => ({
              equipmentManufacturerId,
            })) || []),
            ...(create?.rawData?.map((equipmentManufacturerRawDataId) => ({
              equipmentManufacturerRawDataId,
            })) || []),
          ],
        },
        deleteMany: {
          ...(remove?.master?.length
            ? {
                equipmentManufacturerId: {
                  in: remove.master,
                },
              }
            : {}),
          ...(remove?.rawData?.length
            ? {
                equipmentManufacturerRawDataId: {
                  in: remove.rawData,
                },
              }
            : {}),
        },
      };
    }

    if (equipmentModels?.length) {
      const { create, remove } = separateCreateAndDeleteData(equipmentModels, existingJob.JobEquipmentModel);

      input.JobEquipmentModel = {
        createMany: {
          data: [
            ...(create?.master?.map((equipmentModelId) => ({
              equipmentModelId,
            })) || []),
            ...(create?.rawData?.map((equipmentModelRawDataId) => ({
              equipmentModelRawDataId,
            })) || []),
          ],
        },
        deleteMany: {
          ...(remove?.master?.length
            ? {
                equipmentModelId: {
                  in: remove.master,
                },
              }
            : {}),
          ...(remove?.rawData?.length
            ? {
                equipmentModelRawDataId: {
                  in: remove.rawData,
                },
              }
            : {}),
        },
      };
    }
    if (typeof expiryDate !== 'undefined') {
      input.expiryDate = expiryDate;
    }
    if (typeof isUrgent === 'boolean') {
      input.isUrgent = isUrgent;
    }
    if (ship?.dataType === 'master') {
      input.shipImo = ship.imo;
    } else if (ship?.dataType === 'raw') {
      input.shipRawDataImo = ship.imo;
    }
    if (typeof minYears === 'number') {
      input.minYears = minYears;
    }
    if (typeof maxYears === 'number') {
      input.maxYears = maxYears;
    }
    const result = await prismaPG.job.update({
      data: input,
      where: {
        id: jobId,
      },
      include: {
        Entity: true,
        EntityRawData: true,
        DesignationAlternative: true,
        DesignationRawData: true,
        DepartmentAlternative: true,
        DepartmentRawData: true,
        Ship: true,
        ShipRawData: true,
        Profile: {
          select: {
            id: true,
            name: true,
            avatar: true,
            designationText: true,
            entityText: true,
          },
        },
      },
    });

    if (!result) {
      throw new AppError('JOB001');
    }

    try {
      await updateDocument('jobs', result.id, {
        isOfficial: result.isOfficial,
        isUrgent: result.isUrgent,
        status: result.status,
        expiryDate: result.expiryDate,
        minYears: result.minYears,
        maxYears: result.maxYears,
        minSalary: result.minSalary,
        maxSalary: result.maxSalary,
        entityId: result.entityId || result.entityRawDataId,
        entityName: result.Entity?.name || result.EntityRawData?.name || null,
        designationId: result.designationAlternativeId || result.designationRawDataId,
        designationName: result.DesignationAlternative?.name || result.DesignationRawData?.name || null,
        departmentId: result.departmentAlternativeId || result.departmentRawDataId,
        departmentName: result.DepartmentAlternative?.name || result.DepartmentRawData?.name || null,
        shipId: result.shipImo || result.shipRawDataImo,
        shipName: result.Ship?.name || result.ShipRawData?.name || null,
        creatorId: result.profileId,
        creatorName: result.Profile?.name || null,
        creatorAvatar: result.Profile?.avatar || null,
        creatorDesignationText: result.Profile?.designationText || null,
        creatorEntityText: result.Profile?.entityText || null,
      });
    } catch (esError) {
      console.warn('Elasticsearch sync failed:', esError);
    }

    return pick(result, ['id']);
  },
  deleteOne: async (state: FastifyStateI, { id: jobId }: RouteParamsI) => {
    const selfProfileId = state.profileId;
    const existingJob = await prismaPG.job.findUnique({
      select: {
        id: true,
        profileId: true,
        isOfficial: true,
        entityId: true,
        entityRawDataId: true,
      },
      where: {
        id: jobId,
      },
    });
    if (!existingJob) {
      throw new AppError('JOB008');
    }
    if (existingJob.profileId !== selfProfileId) {
      throw new AppError('JOB009');
    }
    if (existingJob.isOfficial) {
      await EntityMemberModule.isAnyMember({
        entity: existingJob.entityId
          ? {
              id: existingJob.entityId,
              dataType: 'master',
            }
          : {
              id: existingJob.entityRawDataId,
              dataType: 'raw',
            },
        profileId: selfProfileId,
      });
    }
    const result = await prismaPG.job.delete({
      where: {
        id: jobId,
      },
      select: {
        id: true,
      },
    });
    if (!result) {
      throw new AppError('JOB001');
    }

    try {
      await deleteDocument('jobs', jobId);
    } catch (esError) {
      console.warn('Elasticsearch sync failed:', esError);
    }

    return pick(result, ['id']);
  },
  isExpiryDateValid: async ({
    appConfig,
    expiryDate,
    existingJob,
    isUrgent,
  }: {
    appConfig: CompanyJobConfigI;
    expiryDate: Date;
    existingJob?: Job.Job;
    isUrgent: boolean;
  }) => {
    const currentDate = getDateOnly();
    expiryDate = getDateOnly(expiryDate);
    if ((typeof isUrgent === 'boolean' && isUrgent) || (typeof isUrgent === 'undefined' && existingJob?.isUrgent)) {
      const allowedMinExpiryDate = addDaysToDateOnly({ date: currentDate, days: appConfig.urgentMinExpiry.value });
      if (expiryDate < allowedMinExpiryDate) {
        throw new AppError('JOB004');
      }
    } else {
      const allowedMinExpiryDate = addDaysToDateOnly({ date: currentDate, days: appConfig.nonUrgentMinExpiry.value });
      if (expiryDate < allowedMinExpiryDate) {
        throw new AppError('JOB005');
      }
    }
  },
  searchForCandidate: async (
    state: FastifyStateI,
    {
      applicationStatus,
      cursorId,
      entity,
      jobId,
      isOfficial,
      pageSize,
      status,
      designations,
      countries,
      shipTypes,
      internetLimits
    }: Job.SearchForCandidateI,
  ): Promise<CursorDataI<Job.SearchForCandidateResultI>> => {
    const selfProfileId = state.profileId;
    const where: Prisma.Sql[] = [];
    if (cursorId) {
      where.push(Prisma.sql`j."cursorId" < ${cursorId}`);
    }
    if (typeof isOfficial === 'boolean') {
      where.push(Prisma.sql`j."isOfficial" = ${isOfficial}`);
    }
    if (status) {
      where.push(Prisma.sql`j."status" < ${status}`);
    }

    if (jobId) {
      where.push(Prisma.sql`j."id" = ${jobId}::uuid`);
    }

    if (designations?.length) {
      const designationAlternativeIds = designations.filter((d) => d.dataType === 'master').map((d) => d.id);

      const designationRawDataIds = designations.filter((d) => d.dataType === 'raw').map((d) => d.id);

      if (designationAlternativeIds.length) {
        where.push(
          Prisma.sql`j."designationAlternativeId" IN (${Prisma.join(
            designationAlternativeIds.map((id) => Prisma.sql`${id}::uuid`),
            ', ',
          )})`,
        );
      }

      if (designationRawDataIds.length) {
        where.push(
          Prisma.sql`j."designationRawDataId" IN (${Prisma.join(
            designationRawDataIds.map((id) => Prisma.sql`${id}::uuid`),
            ', ',
          )})`,
        );
      }
    }

    if (countries?.length) {
      where.push(Prisma.sql`j."countryIso2" IN (${Prisma.join(countries)})`);
    }

    if (shipTypes?.length) {
      where.push(
        Prisma.sql`s."mainVesselTypeId" IN (${Prisma.join(
          shipTypes.map((id) => Prisma.sql`${id}::uuid`),
          ', ',
        )})`,
      );
    }

    if (internetLimits?.length) {
      const internetConditions: Prisma.Sql[] = [];
      internetLimits.forEach((limit) => {
        switch (limit.id) {
          case '0':
            internetConditions.push(Prisma.sql`(jb."internetAvailable" = false OR jb."internetAvailable" IS NULL)`);
            break;
          case '<200':
            internetConditions.push(Prisma.sql`(jb."internetAvailable" = true AND jb."internetLimitPerDay" < 200)`);
            break;
          case '200-500':
            internetConditions.push(Prisma.sql`(jb."internetAvailable" = true AND jb."internetLimitPerDay" BETWEEN 200 AND 500)`);
            break;
          case '500-1000':
            internetConditions.push(Prisma.sql`(jb."internetAvailable" = true AND jb."internetLimitPerDay" BETWEEN 501 AND 1000)`);
            break;
          case '1000+':
            internetConditions.push(Prisma.sql`(jb."internetAvailable" = true AND jb."internetLimitPerDay" > 1000)`);
            break;
          case 'unknown':
            internetConditions.push(Prisma.sql`(jb."internetAvailable" IS NULL AND jb."internetLimitPerDay" IS NULL)`);
            break;
        }
      });

      if (internetConditions.length) {
        where.push(Prisma.sql`(${Prisma.join(internetConditions, ' OR ')})`);
      }
    }

    const sqlResult = await prismaPG.$queryRaw<Job.SearchForCandidateSQLI[]>`
   SELECT
     j."id" as "jobId",
     j."cursorId",
     j."isUrgent",
     j."entityId",
     e."name" as "entityName",
     j."entityRawDataId",
     er."name" as "entityRawDataName",

     j."designationAlternativeId",
     dsg."name" as "designationName",
     j."designationRawDataId",
     dsgr."name" as "designationRawDataName",

     j."departmentAlternativeId",
     dep."name" as "departmentName",
     j."departmentRawDataId",
     depr."name" as "departmentRawDataName",

     j."shipImo",
     s."name" as "shipName",
     j."shipRawDataImo",
     sr."name" as "shipRawDataName",
     j."showShipDetails",
     j."applicationMethod",
     j."applicationEmail",
     j."applicationUrl",
     j."minYears",
     j."maxYears",
     j."minSalary",
     j."maxSalary",
     j."status",
     j."expiryDate",
     j."isOfficial",
     j."createdAt",
     a.status AS "applicationStatus",
     creator."id" AS "creatorId",
     creator."name" AS "creatorName",
     creator."avatar" AS "creatorAvatar",
     creator."designationText" AS "creatorDesignationText",
     creator."designationAlternativeId" AS "creatorDesignationAlternativeId",
     creator."designationRawDataId" AS "creatorDesignationRawDataId",
     creator."entityText" AS "creatorEntityText",
     creator."entityId" AS "creatorEntityId",
     creator."entityRawDataId" AS "creatorEntityRawDataId",
     ${JobUtil.Sql.getMatching(selfProfileId)}

   FROM "company"."Job" j

    LEFT JOIN "company"."DesignationAlternative" dsg ON j."designationAlternativeId"::uuid = dsg."id"::uuid
    LEFT JOIN "rawData"."DesignationRawData" dsgr ON j."designationRawDataId"::uuid = dsgr."id"::uuid

    LEFT JOIN "company"."DepartmentAlternative" dep ON j."departmentAlternativeId"::uuid = dep."id"::uuid
    LEFT JOIN "rawData"."DepartmentRawData" depr ON j."departmentRawDataId"::uuid = depr."id"::uuid

    LEFT JOIN "master"."Country" mrc ON j."countryIso2" = mrc."iso2"

    LEFT JOIN "company"."Entity" e ON j."entityId"::uuid = e."id"::uuid
    ${entity ? (entity.dataType === 'master' ? Prisma.sql`AND j."entityId"::uuid = ${entity.id}::uuid` : Prisma.empty) : Prisma.empty}
    LEFT JOIN "rawData"."EntityRawData" er ON j."entityRawDataId" = er."id"
    ${entity ? (entity.dataType === 'raw' ? Prisma.sql`AND j."entityRawDataId"::uuid = ${entity.id}::uuid` : Prisma.empty) : Prisma.empty}

    INNER JOIN "user"."Profile" creator ON j."profileId"::uuid = creator."id"::uuid

    LEFT JOIN "company"."JobApplication" a ON j."id"::uuid = a."jobId"::uuid AND a."applicantId"::uuid = ${selfProfileId}::uuid ${applicationStatus ? Prisma.sql` AND a."status" = ${applicationStatus}` : Prisma.empty}
    LEFT JOIN "company"."JobBenefit" jb ON j."id"::uuid = jb."jobId"::uuid

   LEFT JOIN "ship"."Ship" s ON j."shipImo" = s."imo"
   LEFT JOIN "rawData"."ShipRawData" sr ON j."shipRawDataImo" = sr."imo"

    ${where?.length ? Prisma.sql`WHERE ${Prisma.join(where, ' AND ')}` : Prisma.empty}

   ORDER BY j."cursorId" DESC
   ${jobId ? Prisma.empty : Prisma.sql`LIMIT ${pageSize + 1}`}
 `;
    const result: Job.SearchForCandidateResultI[] = [];
    if (sqlResult?.length) {
      result.push(
        ...(sqlResult.slice(0, pageSize) ?? []).map(
          ({
            jobId,
            cursorId,
            isUrgent,
            entityId,
            entityName,
            entityRawDataId,
            entityRawDataName,
            designationAlternativeId,
            designationName,
            designationRawDataId,
            designationRawDataName,
            departmentAlternativeId,
            departmentName,
            departmentRawDataId,
            departmentRawDataName,
            shipImo,
            shipName,
            shipRawDataImo,
            shipRawDataName,
            showShipDetails,
            applicationMethod,
            applicationEmail,
            applicationUrl,
            minYears,
            maxYears,
            minSalary,
            maxSalary,
            status,
            expiryDate,
            isOfficial,
            createdAt,
            applicationStatus,
            creatorId,
            creatorName,
            creatorAvatar,
            creatorDesignationText,
            creatorDesignationAlternativeId,
            creatorDesignationRawDataId,
            creatorEntityText,
            creatorEntityId,
            creatorEntityRawDataId,
            matching,
          }: Job.SearchForCandidateSQLI) =>
            ({
              id: jobId,
              cursorId: cursorId.toString(),
              isUrgent,
              creator: User.ProfileModule.transformProfile({
                id: creatorId,
                name: creatorName,
                avatar: creatorAvatar,
                designationText: creatorDesignationText,
                entityText: creatorEntityText,
                designationAlternativeId: creatorDesignationAlternativeId,
                designationRawDataId: creatorDesignationRawDataId,
                entityId: creatorEntityId,
                entityRawDataId: creatorEntityRawDataId,
              }),
              entity: EntityModule.transform({
                entityId,
                entityName,
                entityRawDataId,
                entityRawDataName,
              }),
              designation: DesignationModule.transform({
                designationAlternativeId,
                designationName,
                designationRawDataId,
                designationRawDataName,
              }),
              department: DepartmentModule.transform({
                departmentAlternativeId,
                departmentName,
                departmentRawDataId,
                departmentRawDataName,
              }),
              ship: showShipDetails ? Ship.CoreShipModule.transform({
                shipImo,
                shipName,
                shipRawDataImo,
                shipRawDataName,
              }) : null,
              applicationMethod: applicationMethod as 'IN_APP' | 'EMAIL' | 'EXTERNAL_LINK',
              applicationEmail,
              applicationUrl,
              minYears,
              maxYears,
              minSalary,
              maxSalary,
              status,
              expiryDate,
              isOfficial,
              createdAt,
              applicationStatus,
              matching: NumberUtil.ceilToTwoDecimals(matching || 0),
            }) as Job.SearchForCandidateResultI,
        ),
      );
    }
    return { data: result, nextCursorId: result.length ? Number(result[result.length - 1].cursorId) : null };
  },
  fetchOneForCandidate: async (
    state: FastifyStateI,
    { id: jobId }: RouteParamsI,
  ): Promise<Job.FetchOneForCandidateResultI> => {
    const selfProfileId = state.profileId;

    // First, fetch basic job details to determine requirement and benefit types
    const basicJobDetails = await prismaPG.job.findUnique({
      where: { id: jobId },
      select: {
        id: true,
        requirementType: true,
        JobBenefit: {
          select: {
            benefitType: true,
          },
        },
      },
    });

    if (!basicJobDetails) {
      throw new AppError('JOB008');
    }

    const requirementType = basicJobDetails.requirementType || 'BASIC';
    const benefitType = basicJobDetails.JobBenefit?.benefitType || 'BASIC';

    // different combinations of requirement and benefit types hanled diffrently  for query efficiency
    if (requirementType === 'BASIC' && benefitType === 'BASIC') {
      return await JobModule.fetchOneForCandidateBasic(state, jobId, selfProfileId);
    } else if (requirementType === 'BASIC' && benefitType === 'ADVANCED') {
      return await JobModule.fetchOneForCandidateBasicReqAdvancedBen(state, jobId, selfProfileId);
    } else if (requirementType === 'ADVANCED' && benefitType === 'BASIC') {
      return await JobModule.fetchOneForCandidateAdvancedReqBasicBen(state, jobId, selfProfileId);
    } else if (requirementType === 'ADVANCED' && benefitType === 'ADVANCED') {
      return await JobModule.fetchOneForCandidateAdvanced(state, jobId, selfProfileId);
    }

    return await JobModule.fetchOneForCandidateBasic(state, jobId, selfProfileId);
  },

  fetchOneForCandidateBasic: async (
    state: FastifyStateI,
    jobId: string,
    selfProfileId: string,
  ): Promise<Job.FetchOneForCandidateResultI> => {
    const sqlResult = await prismaPG.$queryRaw<Job.FetchOneForCandidateSQLI[]>`
      SELECT
        j."id" as "jobId",
        j."cursorId",
        j."isUrgent",
        j."entityId",
        e."name" as "entityName",
        j."entityRawDataId",
        er."name" as "entityRawDataName",

        j."designationAlternativeId",
        dsg."name" as "designationName",
        j."designationRawDataId",
        dsgr."name" as "designationRawDataName",

        j."departmentAlternativeId",
        dep."name" as "departmentName",
        j."departmentRawDataId",
        depr."name" as "departmentRawDataName",

        j."shipImo",
        s."name" as "shipName",
        j."shipRawDataImo",
        sr."name" as "shipRawDataName",
        j."showShipDetails",
        j."applicationMethod",
        j."applicationEmail",
        j."applicationUrl",
        j."minYears",
        j."maxYears",
        j."minSalary",
        j."maxSalary",
        j."status",
        j."expiryDate",
        j."isOfficial",
        j."createdAt",
        j."about",
        j."rolesResponsibilities", 
        j."requirements" as "basicRequirements",
        j."benefits" as "basicBenefits",
        j."salaryType",
        j."currencyCode",
        j."showSalary",
        j."jobType",
        j."countryIso2",
        j."joiningDate",
        j."genderDiversityIndex",
        j."requirementType",
        COALESCE(jb."benefitType", 'BASIC') as "benefitType",

        -- Application status
        a.status AS "applicationStatus",

        -- Creator details
        creator."id" AS "creatorId",
        creator."name" AS "creatorName",
        creator."avatar" AS "creatorAvatar",
        creator."designationText" AS "creatorDesignationText",
        creator."designationAlternativeId" AS "creatorDesignationAlternativeId",
        creator."designationRawDataId" AS "creatorDesignationRawDataId",
        creator."entityText" AS "creatorEntityText",
        creator."entityId" AS "creatorEntityId",
        creator."entityRawDataId" AS "creatorEntityRawDataId",

        -- Country details
        country."name" as "countryName",

        -- Matching score
        ${JobUtil.Sql.getMatching(selfProfileId)}

      FROM "company"."Job" j

      LEFT JOIN "company"."DesignationAlternative" dsg ON j."designationAlternativeId"::uuid = dsg."id"::uuid
      LEFT JOIN "rawData"."DesignationRawData" dsgr ON j."designationRawDataId"::uuid = dsgr."id"::uuid

      LEFT JOIN "company"."DepartmentAlternative" dep ON j."departmentAlternativeId"::uuid = dep."id"::uuid
      LEFT JOIN "rawData"."DepartmentRawData" depr ON j."departmentRawDataId"::uuid = depr."id"::uuid

      LEFT JOIN "company"."Entity" e ON j."entityId"::uuid = e."id"::uuid
      LEFT JOIN "rawData"."EntityRawData" er ON j."entityRawDataId" = er."id"

      LEFT JOIN "ship"."Ship" s ON j."shipImo" = s."imo"
      LEFT JOIN "rawData"."ShipRawData" sr ON j."shipRawDataImo" = sr."imo"

      LEFT JOIN "master"."Country" country ON j."countryIso2" = country."iso2"

      -- Creator profile
      INNER JOIN "user"."Profile" creator ON j."profileId"::uuid = creator."id"::uuid

      -- JobBenefit for benefit type
      LEFT JOIN "company"."JobBenefit" jb ON j."id"::uuid = jb."jobId"::uuid

      -- Application status
      LEFT JOIN "company"."JobApplication" a ON j."id"::uuid = a."jobId"::uuid AND a."applicantId"::uuid = ${selfProfileId}::uuid

      WHERE j."id"::uuid = ${jobId}::uuid
    `;

    if (!sqlResult?.length) {
      throw new AppError('JOB008');
    }

    const jobData = sqlResult[0];

    const result: Job.FetchOneForCandidateResultI = {
      id: jobData.jobId,
      cursorId: jobData.cursorId.toString(),
      isUrgent: jobData.isUrgent,
      entity: EntityModule.transform({
        entityId: jobData.entityId,
        entityName: jobData.entityName,
        entityRawDataId: jobData.entityRawDataId,
        entityRawDataName: jobData.entityRawDataName,
      }),
      designation: DesignationModule.transform({
        designationAlternativeId: jobData.designationAlternativeId,
        designationName: jobData.designationName,
        designationRawDataId: jobData.designationRawDataId,
        designationRawDataName: jobData.designationRawDataName,
      }),
      department: DepartmentModule.transform({
        departmentAlternativeId: jobData.departmentAlternativeId,
        departmentName: jobData.departmentName,
        departmentRawDataId: jobData.departmentRawDataId,
        departmentRawDataName: jobData.departmentRawDataName,
      }),
      ship: jobData.showShipDetails ? Ship.CoreShipModule.transform({
        shipImo: jobData.shipImo,
        shipName: jobData.shipName,
        shipRawDataImo: jobData.shipRawDataImo,
        shipRawDataName: jobData.shipRawDataName,
      }) : null,
      applicationMethod: jobData.applicationMethod as 'IN_APP' | 'EMAIL' | 'EXTERNAL_LINK',
      applicationEmail: jobData.applicationEmail,
      applicationUrl: jobData.applicationUrl,
      minYears: jobData.minYears,
      maxYears: jobData.maxYears,
      minSalary: jobData.minSalary,
      maxSalary: jobData.maxSalary,
      status: jobData.status as JobE.StatusI,
      expiryDate: jobData.expiryDate,
      isOfficial: jobData.isOfficial,
      createdAt: jobData.createdAt,
      applicationStatus: jobData.applicationStatus as ApplicationE.StatusI,
      
      // Basic job details
      about: jobData.about,
      rolesResponsibilities: jobData.rolesResponsibilities,
      requirements: jobData.basicRequirements,
      benefits: jobData.basicBenefits,
      salaryType: jobData.salaryType,
      currencyCode: jobData.currencyCode,
      showSalary: jobData.showSalary,
      jobType: jobData.jobType,
      countryIso2: jobData.countryIso2,
      joiningDate: jobData.joiningDate,
      genderDiversityIndex: jobData.genderDiversityIndex,
      countryName: jobData.countryName,
      requirementType: jobData.requirementType,
      benefitType: jobData.benefitType,
      showShipDetails: jobData.showShipDetails,
      
      creator: User.ProfileModule.transformProfile({
        id: jobData.creatorId,
        name: jobData.creatorName,
        avatar: jobData.creatorAvatar,
        designationText: jobData.creatorDesignationText,
        entityText: jobData.creatorEntityText,
        designationAlternativeId: jobData.creatorDesignationAlternativeId,
        designationRawDataId: jobData.creatorDesignationRawDataId,
        entityId: jobData.creatorEntityId,
        entityRawDataId: jobData.creatorEntityRawDataId,
      }),
      matching: NumberUtil.ceilToTwoDecimals(jobData.matching || 0),
    };

    return result;
  },

  fetchOneForCandidateBasicReqAdvancedBen: async (
    state: FastifyStateI,
    jobId: string,
    selfProfileId: string,
  ): Promise<Job.FetchOneForCandidateResultI> => {
    const sqlResult = await prismaPG.$queryRaw<Job.FetchOneForCandidateSQLI[]>`
      SELECT
        j."id" as "jobId",
        j."cursorId",
        j."isUrgent",
        j."entityId",
        e."name" as "entityName",
        j."entityRawDataId",
        er."name" as "entityRawDataName",

        j."designationAlternativeId",
        dsg."name" as "designationName",
        j."designationRawDataId",
        dsgr."name" as "designationRawDataName",

        j."departmentAlternativeId",
        dep."name" as "departmentName",
        j."departmentRawDataId",
        depr."name" as "departmentRawDataName",

        j."shipImo",
        s."name" as "shipName",
        j."shipRawDataImo",
        sr."name" as "shipRawDataName",
        j."showShipDetails",
        j."applicationMethod",
        j."applicationEmail",
        j."applicationUrl",
        j."minYears",
        j."maxYears",
        j."minSalary",
        j."maxSalary",
        j."status",
        j."expiryDate",
        j."isOfficial",
        j."createdAt",
        j."about",
        j."rolesResponsibilities", 
        j."requirements" as "basicRequirements",
        j."salaryType",
        j."currencyCode",
        j."showSalary",
        j."jobType",
        j."countryIso2",
        j."joiningDate",
        j."genderDiversityIndex",
        j."requirementType",
        jb."benefitType",

        -- Advanced benefit details
        jb."contractMonths",
        jb."contractDays",
        jb."internetAvailable",
        jb."internetSpeed",
        jb."internetLimitPerDay",
        jb."internetDetails",
        jb."insuranceType",
        jb."familyOnboard",
        jb."itfType",

        -- Application status
        a.status AS "applicationStatus",

        -- Creator details
        creator."id" AS "creatorId",
        creator."name" AS "creatorName",
        creator."avatar" AS "creatorAvatar",
        creator."designationText" AS "creatorDesignationText",
        creator."designationAlternativeId" AS "creatorDesignationAlternativeId",
        creator."designationRawDataId" AS "creatorDesignationRawDataId",
        creator."entityText" AS "creatorEntityText",
        creator."entityId" AS "creatorEntityId",
        creator."entityRawDataId" AS "creatorEntityRawDataId",

        -- Country details
        country."name" as "countryName",

        -- Matching score
        ${JobUtil.Sql.getMatching(selfProfileId)}

      FROM "company"."Job" j

      LEFT JOIN "company"."DesignationAlternative" dsg ON j."designationAlternativeId"::uuid = dsg."id"::uuid
      LEFT JOIN "rawData"."DesignationRawData" dsgr ON j."designationRawDataId"::uuid = dsgr."id"::uuid

      LEFT JOIN "company"."DepartmentAlternative" dep ON j."departmentAlternativeId"::uuid = dep."id"::uuid
      LEFT JOIN "rawData"."DepartmentRawData" depr ON j."departmentRawDataId"::uuid = depr."id"::uuid

      LEFT JOIN "company"."Entity" e ON j."entityId"::uuid = e."id"::uuid
      LEFT JOIN "rawData"."EntityRawData" er ON j."entityRawDataId" = er."id"

      LEFT JOIN "ship"."Ship" s ON j."shipImo" = s."imo"
      LEFT JOIN "rawData"."ShipRawData" sr ON j."shipRawDataImo" = sr."imo"

      LEFT JOIN "master"."Country" country ON j."countryIso2" = country."iso2"

      -- Creator profile
      INNER JOIN "user"."Profile" creator ON j."profileId"::uuid = creator."id"::uuid

      -- JobBenefit for advanced benefits 
      INNER JOIN "company"."JobBenefit" jb ON j."id"::uuid = jb."jobId"::uuid AND jb."benefitType" = 'ADVANCED'

      -- Application status
      LEFT JOIN "company"."JobApplication" a ON j."id"::uuid = a."jobId"::uuid AND a."applicantId"::uuid = ${selfProfileId}::uuid

      WHERE j."id"::uuid = ${jobId}::uuid
    `;

    if (!sqlResult?.length) {
      throw new AppError('JOB008');
    }

    const jobData = sqlResult[0];

    const result: Job.FetchOneForCandidateResultI = {
      id: jobData.jobId,
      cursorId: jobData.cursorId.toString(),
      isUrgent: jobData.isUrgent,
      entity: EntityModule.transform({
        entityId: jobData.entityId,
        entityName: jobData.entityName,
        entityRawDataId: jobData.entityRawDataId,
        entityRawDataName: jobData.entityRawDataName,
      }),
      designation: DesignationModule.transform({
        designationAlternativeId: jobData.designationAlternativeId,
        designationName: jobData.designationName,
        designationRawDataId: jobData.designationRawDataId,
        designationRawDataName: jobData.designationRawDataName,
      }),
      department: DepartmentModule.transform({
        departmentAlternativeId: jobData.departmentAlternativeId,
        departmentName: jobData.departmentName,
        departmentRawDataId: jobData.departmentRawDataId,
        departmentRawDataName: jobData.departmentRawDataName,
      }),
      ship: jobData.showShipDetails ? Ship.CoreShipModule.transform({
        shipImo: jobData.shipImo,
        shipName: jobData.shipName,
        shipRawDataImo: jobData.shipRawDataImo,
        shipRawDataName: jobData.shipRawDataName,
      }) : null,
      applicationMethod: jobData.applicationMethod as 'IN_APP' | 'EMAIL' | 'EXTERNAL_LINK',
      applicationEmail: jobData.applicationEmail,
      applicationUrl: jobData.applicationUrl,
      minYears: jobData.minYears,
      maxYears: jobData.maxYears,
      minSalary: jobData.minSalary,
      maxSalary: jobData.maxSalary,
      status: jobData.status as JobE.StatusI,
      expiryDate: jobData.expiryDate,
      isOfficial: jobData.isOfficial,
      createdAt: jobData.createdAt,
      applicationStatus: jobData.applicationStatus as ApplicationE.StatusI,
      
      about: jobData.about,
      rolesResponsibilities: jobData.rolesResponsibilities,
      requirements: jobData.basicRequirements,
      benefits: null,
      salaryType: jobData.salaryType,
      currencyCode: jobData.currencyCode,
      showSalary: jobData.showSalary,
      jobType: jobData.jobType,
      countryIso2: jobData.countryIso2,
      joiningDate: jobData.joiningDate,
      genderDiversityIndex: jobData.genderDiversityIndex,
      countryName: jobData.countryName,
      requirementType: jobData.requirementType,
      benefitType: jobData.benefitType,
      showShipDetails: jobData.showShipDetails,

      // Advanced benefit data
      contractMonths: jobData.contractMonths,
      contractDays: jobData.contractDays,
      internetAvailable: jobData.internetAvailable,
      internetSpeed: jobData.internetSpeed,
      internetLimitPerDay: jobData.internetLimitPerDay,
      internetDetails: jobData.internetDetails,
      insuranceType: jobData.insuranceType,
      familyOnboard: jobData.familyOnboard,
      itfType: jobData.itfType,
      
      creator: User.ProfileModule.transformProfile({
        id: jobData.creatorId,
        name: jobData.creatorName,
        avatar: jobData.creatorAvatar,
        designationText: jobData.creatorDesignationText,
        entityText: jobData.creatorEntityText,
        designationAlternativeId: jobData.creatorDesignationAlternativeId,
        designationRawDataId: jobData.creatorDesignationRawDataId,
        entityId: jobData.creatorEntityId,
        entityRawDataId: jobData.creatorEntityRawDataId,
      }),
      matching: NumberUtil.ceilToTwoDecimals(jobData.matching || 0),
    };

    return result;
  },

  fetchOneForCandidateAdvancedReqBasicBen: async (
    state: FastifyStateI,
    jobId: string,
    selfProfileId: string,
  ): Promise<Job.FetchOneForCandidateResultI> => {

    const mainJobResult = await prismaPG.$queryRaw<Job.FetchOneForCandidateSQLI[]>`
      SELECT
        j."id" as "jobId",
        j."cursorId",
        j."isUrgent",
        j."entityId",
        e."name" as "entityName",
        j."entityRawDataId",
        er."name" as "entityRawDataName",

        j."designationAlternativeId",
        dsg."name" as "designationName",
        j."designationRawDataId",
        dsgr."name" as "designationRawDataName",

        j."departmentAlternativeId",
        dep."name" as "departmentName",
        j."departmentRawDataId",
        depr."name" as "departmentRawDataName",

        j."shipImo",
        s."name" as "shipName",
        j."shipRawDataImo",
        sr."name" as "shipRawDataName",
        j."showShipDetails",
        j."applicationMethod",
        j."applicationEmail",
        j."applicationUrl",
        j."minYears",
        j."maxYears",
        j."minSalary",
        j."maxSalary",
        j."status",
        j."expiryDate",
        j."isOfficial",
        j."createdAt",
        
        -- Job details for ADVANCED requirements
        j."about",
        j."rolesResponsibilities", 
        j."benefits" as "basicBenefits",
        j."salaryType",
        j."currencyCode",
        j."showSalary",
        j."jobType",
        j."countryIso2",
        j."joiningDate",
        j."genderDiversityIndex",
        j."requirementType",
        COALESCE(jb."benefitType", 'BASIC') as "benefitType",

        -- Application status
        a.status AS "applicationStatus",

        -- Creator details
        creator."id" AS "creatorId",
        creator."name" AS "creatorName",
        creator."avatar" AS "creatorAvatar",
        creator."designationText" AS "creatorDesignationText",
        creator."designationAlternativeId" AS "creatorDesignationAlternativeId",
        creator."designationRawDataId" AS "creatorDesignationRawDataId",
        creator."entityText" AS "creatorEntityText",
        creator."entityId" AS "creatorEntityId",
        creator."entityRawDataId" AS "creatorEntityRawDataId",

        -- Country details
        country."name" as "countryName",

        -- Matching score
        ${JobUtil.Sql.getMatching(selfProfileId)}

      FROM "company"."Job" j

      LEFT JOIN "company"."DesignationAlternative" dsg ON j."designationAlternativeId"::uuid = dsg."id"::uuid
      LEFT JOIN "rawData"."DesignationRawData" dsgr ON j."designationRawDataId"::uuid = dsgr."id"::uuid

      LEFT JOIN "company"."DepartmentAlternative" dep ON j."departmentAlternativeId"::uuid = dep."id"::uuid
      LEFT JOIN "rawData"."DepartmentRawData" depr ON j."departmentRawDataId"::uuid = depr."id"::uuid

      LEFT JOIN "company"."Entity" e ON j."entityId"::uuid = e."id"::uuid
      LEFT JOIN "rawData"."EntityRawData" er ON j."entityRawDataId" = er."id"

      LEFT JOIN "ship"."Ship" s ON j."shipImo" = s."imo"
      LEFT JOIN "rawData"."ShipRawData" sr ON j."shipRawDataImo" = sr."imo"

      LEFT JOIN "master"."Country" country ON j."countryIso2" = country."iso2"

      -- Creator profile
      INNER JOIN "user"."Profile" creator ON j."profileId"::uuid = creator."id"::uuid

      -- JobBenefit for benefit type
      LEFT JOIN "company"."JobBenefit" jb ON j."id"::uuid = jb."jobId"::uuid

      -- Application status
      LEFT JOIN "company"."JobApplication" a ON j."id"::uuid = a."jobId"::uuid AND a."applicantId"::uuid = ${selfProfileId}::uuid

      WHERE j."id"::uuid = ${jobId}::uuid
    `;

    if (!mainJobResult?.length) {
      throw new AppError('JOB008');
    }

    const jobData = mainJobResult[0];

    // fetch advanced requirements data separately
    const [
      certificationRequirements,
      documentRequirements, 
      experienceRequirements,
      skillRequirements,
      cargoRequirements,
      otherRequirements,
    ] = await Promise.all([
      // Certification requirements
      prismaPG.jobCertificationRequirement.findMany({
        where: { jobId },
        include: {
          CertificateCourse: true,
          CertificateCourseRawData: true,
        },
      }),
      
      // Document requirements  
      prismaPG.jobDocumentRequirement.findMany({
        where: { jobId },
        include: {
          DocumentType: true,
          DocumentTypeRawData: true,
        },
        orderBy: { sequence: 'asc' },
      }),
      
      // Experience requirements
      prismaPG.jobExperienceRequirement.findMany({
        where: { jobId },
        include: {
          DesignationAlternative: true,
          DesignationRawData: true,
          MainVesselType: true,
          MainVesselTypeRawData: true,
        },
      }),
      
      // Skill requirements
      prismaPG.jobSkillRequirement.findMany({
        where: { jobId },
        include: {
          Skill: true,
          SkillRawData: true,
        },
      }),
      
      // Cargo requirements
      prismaPG.jobCargoRequirement.findMany({
        where: { jobId },
      }),
      
      // Other requirements
      prismaPG.jobOtherRequirement.findMany({
        where: { jobId },
        orderBy: { sequenceNumber: 'asc' },
      }),
    ]);

    const result: Job.FetchOneForCandidateResultI = {
      id: jobData.jobId,
      cursorId: jobData.cursorId.toString(),
      isUrgent: jobData.isUrgent,
      entity: EntityModule.transform({
        entityId: jobData.entityId,
        entityName: jobData.entityName,
        entityRawDataId: jobData.entityRawDataId,
        entityRawDataName: jobData.entityRawDataName,
      }),
      designation: DesignationModule.transform({
        designationAlternativeId: jobData.designationAlternativeId,
        designationName: jobData.designationName,
        designationRawDataId: jobData.designationRawDataId,
        designationRawDataName: jobData.designationRawDataName,
      }),
      department: DepartmentModule.transform({
        departmentAlternativeId: jobData.departmentAlternativeId,
        departmentName: jobData.departmentName,
        departmentRawDataId: jobData.departmentRawDataId,
        departmentRawDataName: jobData.departmentRawDataName,
      }),
      ship: jobData.showShipDetails ? Ship.CoreShipModule.transform({
        shipImo: jobData.shipImo,
        shipName: jobData.shipName,
        shipRawDataImo: jobData.shipRawDataImo,
        shipRawDataName: jobData.shipRawDataName,
      }) : null,
      applicationMethod: jobData.applicationMethod as 'IN_APP' | 'EMAIL' | 'EXTERNAL_LINK',
      applicationEmail: jobData.applicationEmail,
      applicationUrl: jobData.applicationUrl,
      minYears: jobData.minYears,
      maxYears: jobData.maxYears,
      minSalary: jobData.minSalary,
      maxSalary: jobData.maxSalary,
      status: jobData.status as JobE.StatusI,
      expiryDate: jobData.expiryDate,
      isOfficial: jobData.isOfficial,
      createdAt: jobData.createdAt,
      applicationStatus: jobData.applicationStatus as ApplicationE.StatusI,
      
      // Job details
      about: jobData.about,
      rolesResponsibilities: jobData.rolesResponsibilities,
      requirements: null,
      benefits: jobData.basicBenefits,
      salaryType: jobData.salaryType,
      currencyCode: jobData.currencyCode,
      showSalary: jobData.showSalary,
      jobType: jobData.jobType,
      countryIso2: jobData.countryIso2,
      joiningDate: jobData.joiningDate,
      genderDiversityIndex: jobData.genderDiversityIndex,
      countryName: jobData.countryName,
      requirementType: jobData.requirementType,
      benefitType: jobData.benefitType,
      showShipDetails: jobData.showShipDetails,

      // Advanced requirements data
      certificationRequirements,
      documentRequirements,
      experienceRequirements,
      skillRequirements,
      cargoRequirements,
      otherRequirements,
      
      creator: User.ProfileModule.transformProfile({
        id: jobData.creatorId,
        name: jobData.creatorName,
        avatar: jobData.creatorAvatar,
        designationText: jobData.creatorDesignationText,
        entityText: jobData.creatorEntityText,
        designationAlternativeId: jobData.creatorDesignationAlternativeId,
        designationRawDataId: jobData.creatorDesignationRawDataId,
        entityId: jobData.creatorEntityId,
        entityRawDataId: jobData.creatorEntityRawDataId,
      }),
      matching: NumberUtil.ceilToTwoDecimals(jobData.matching || 0),
    };

    return result;
  },

  fetchOneForCandidateAdvanced: async (
    state: FastifyStateI,
    jobId: string,
    selfProfileId: string,
  ): Promise<Job.FetchOneForCandidateResultI> => {
    
    const mainJobResult = await prismaPG.$queryRaw<Job.FetchOneForCandidateSQLI[]>`
      SELECT
        j."id" as "jobId",
        j."cursorId",
        j."isUrgent",
        j."entityId",
        e."name" as "entityName",
        j."entityRawDataId",
        er."name" as "entityRawDataName",

        j."designationAlternativeId",
        dsg."name" as "designationName",
        j."designationRawDataId",
        dsgr."name" as "designationRawDataName",

        j."departmentAlternativeId",
        dep."name" as "departmentName",
        j."departmentRawDataId",
        depr."name" as "departmentRawDataName",

        j."shipImo",
        s."name" as "shipName",
        j."shipRawDataImo",
        sr."name" as "shipRawDataName",
        j."showShipDetails",
        j."applicationMethod",
        j."applicationEmail",
        j."applicationUrl",
        j."minYears",
        j."maxYears",
        j."minSalary",
        j."maxSalary",
        j."status",
        j."expiryDate",
        j."isOfficial",
        j."createdAt",
        
        -- Job details for ADVANCED requirements
        j."about",
        j."rolesResponsibilities", 
        j."salaryType",
        j."currencyCode",
        j."showSalary",
        j."jobType",
        j."countryIso2",
        j."joiningDate",
        j."genderDiversityIndex",
        j."requirementType",
        jb."benefitType",

        -- Advanced benefit details
        jb."contractMonths",
        jb."contractDays",
        jb."internetAvailable",
        jb."internetSpeed",
        jb."internetLimitPerDay",
        jb."internetDetails",
        jb."insuranceType",
        jb."familyOnboard",
        jb."itfType",

        -- Application status
        a.status AS "applicationStatus",

        -- Creator details
        creator."id" AS "creatorId",
        creator."name" AS "creatorName",
        creator."avatar" AS "creatorAvatar",
        creator."designationText" AS "creatorDesignationText",
        creator."designationAlternativeId" AS "creatorDesignationAlternativeId",
        creator."designationRawDataId" AS "creatorDesignationRawDataId",
        creator."entityText" AS "creatorEntityText",
        creator."entityId" AS "creatorEntityId",
        creator."entityRawDataId" AS "creatorEntityRawDataId",

        -- Country details
        country."name" as "countryName",

        -- Matching score
        ${JobUtil.Sql.getMatching(selfProfileId)}

      FROM "company"."Job" j

      LEFT JOIN "company"."DesignationAlternative" dsg ON j."designationAlternativeId"::uuid = dsg."id"::uuid
      LEFT JOIN "rawData"."DesignationRawData" dsgr ON j."designationRawDataId"::uuid = dsgr."id"::uuid

      LEFT JOIN "company"."DepartmentAlternative" dep ON j."departmentAlternativeId"::uuid = dep."id"::uuid
      LEFT JOIN "rawData"."DepartmentRawData" depr ON j."departmentRawDataId"::uuid = depr."id"::uuid

      LEFT JOIN "company"."Entity" e ON j."entityId"::uuid = e."id"::uuid
      LEFT JOIN "rawData"."EntityRawData" er ON j."entityRawDataId" = er."id"

      LEFT JOIN "ship"."Ship" s ON j."shipImo" = s."imo"
      LEFT JOIN "rawData"."ShipRawData" sr ON j."shipRawDataImo" = sr."imo"

      LEFT JOIN "master"."Country" country ON j."countryIso2" = country."iso2"

      -- Creator profile
      INNER JOIN "user"."Profile" creator ON j."profileId"::uuid = creator."id"::uuid

      -- JobBenefit for advanced benefits
      INNER JOIN "company"."JobBenefit" jb ON j."id"::uuid = jb."jobId"::uuid AND jb."benefitType" = 'ADVANCED'

      -- Application status
      LEFT JOIN "company"."JobApplication" a ON j."id"::uuid = a."jobId"::uuid AND a."applicantId"::uuid = ${selfProfileId}::uuid

      WHERE j."id"::uuid = ${jobId}::uuid
    `;

    if (!mainJobResult?.length) {
      throw new AppError('JOB008');
    }

    const jobData = mainJobResult[0];

    const [
      certificationRequirements,
      documentRequirements, 
      experienceRequirements,
      skillRequirements,
      cargoRequirements,
      otherRequirements,
      benefitDetails,
    ] = await Promise.all([
      // Certification requirements
      prismaPG.jobCertificationRequirement.findMany({
        where: { jobId },
        include: {
          CertificateCourse: true,
          CertificateCourseRawData: true,
        },
      }),
      
      // Document requirements  
      prismaPG.jobDocumentRequirement.findMany({
        where: { jobId },
        include: {
          DocumentType: true,
          DocumentTypeRawData: true,
        },
        orderBy: { sequence: 'asc' },
      }),
      
      // Experience requirements
      prismaPG.jobExperienceRequirement.findMany({
        where: { jobId },
        include: {
          DesignationAlternative: true,
          DesignationRawData: true,
          MainVesselType: true,
          MainVesselTypeRawData: true,
        },
      }),
      
      // Skill requirements
      prismaPG.jobSkillRequirement.findMany({
        where: { jobId },
        include: {
          Skill: true,
          SkillRawData: true,
        },
      }),
      
      // Cargo requirements
      prismaPG.jobCargoRequirement.findMany({
        where: { jobId },
      }),
      
      // Other requirements
      prismaPG.jobOtherRequirement.findMany({
        where: { jobId },
        orderBy: { sequenceNumber: 'asc' },
      }),

      // Advanced benefit details
      prismaPG.jobBenefitDetail.findMany({
        where: { 
          JobBenefit: {
            jobId: jobId,
          }
        },
        orderBy: { sequenceNumber: 'asc' },
      }),
    ]);

    const result: Job.FetchOneForCandidateResultI = {
      id: jobData.jobId,
      cursorId: jobData.cursorId.toString(),
      isUrgent: jobData.isUrgent,
      entity: EntityModule.transform({
        entityId: jobData.entityId,
        entityName: jobData.entityName,
        entityRawDataId: jobData.entityRawDataId,
        entityRawDataName: jobData.entityRawDataName,
      }),
      designation: DesignationModule.transform({
        designationAlternativeId: jobData.designationAlternativeId,
        designationName: jobData.designationName,
        designationRawDataId: jobData.designationRawDataId,
        designationRawDataName: jobData.designationRawDataName,
      }),
      department: DepartmentModule.transform({
        departmentAlternativeId: jobData.departmentAlternativeId,
        departmentName: jobData.departmentName,
        departmentRawDataId: jobData.departmentRawDataId,
        departmentRawDataName: jobData.departmentRawDataName,
      }),
      ship: jobData.showShipDetails ? Ship.CoreShipModule.transform({
        shipImo: jobData.shipImo,
        shipName: jobData.shipName,
        shipRawDataImo: jobData.shipRawDataImo,
        shipRawDataName: jobData.shipRawDataName,
      }) : null,
      applicationMethod: jobData.applicationMethod as 'IN_APP' | 'EMAIL' | 'EXTERNAL_LINK',
      applicationEmail: jobData.applicationEmail,
      applicationUrl: jobData.applicationUrl,
      minYears: jobData.minYears,
      maxYears: jobData.maxYears,
      minSalary: jobData.minSalary,
      maxSalary: jobData.maxSalary,
      status: jobData.status as JobE.StatusI,
      expiryDate: jobData.expiryDate,
      isOfficial: jobData.isOfficial,
      createdAt: jobData.createdAt,
      applicationStatus: jobData.applicationStatus as ApplicationE.StatusI,
      
      // Job details
      about: jobData.about,
      rolesResponsibilities: jobData.rolesResponsibilities,
      requirements: null,
      benefits: null,
      salaryType: jobData.salaryType,
      currencyCode: jobData.currencyCode,
      showSalary: jobData.showSalary,
      jobType: jobData.jobType,
      countryIso2: jobData.countryIso2,
      joiningDate: jobData.joiningDate,
      genderDiversityIndex: jobData.genderDiversityIndex,
      countryName: jobData.countryName,
      requirementType: jobData.requirementType,
      benefitType: jobData.benefitType,
      showShipDetails: jobData.showShipDetails,

      // Advanced benefits data
      contractMonths: jobData.contractMonths,
      contractDays: jobData.contractDays,
      internetAvailable: jobData.internetAvailable,
      internetSpeed: jobData.internetSpeed,
      internetLimitPerDay: jobData.internetLimitPerDay,
      internetDetails: jobData.internetDetails,
      insuranceType: jobData.insuranceType,
      familyOnboard: jobData.familyOnboard,
      itfType: jobData.itfType,

      // Advanced requirements data
      certificationRequirements,
      documentRequirements,
      experienceRequirements,
      skillRequirements,
      cargoRequirements,
      otherRequirements,

      // Advanced benefit details
      benefitDetails,
      
      creator: User.ProfileModule.transformProfile({
        id: jobData.creatorId,
        name: jobData.creatorName,
        avatar: jobData.creatorAvatar,
        designationText: jobData.creatorDesignationText,
        entityText: jobData.creatorEntityText,
        designationAlternativeId: jobData.creatorDesignationAlternativeId,
        designationRawDataId: jobData.creatorDesignationRawDataId,
        entityId: jobData.creatorEntityId,
        entityRawDataId: jobData.creatorEntityRawDataId,
      }),
      matching: NumberUtil.ceilToTwoDecimals(jobData.matching || 0),
    };

    return result;
  },

  createJobDetails: async (state: FastifyStateI, data: Job.CreateJobDetailsI) => {
    const selfProfileId = state.profileId;

    const [
      appConfig,
      _entityResult,
      _designationResult,
      _shipResult,
      _isAnyMemberResult,
      entityProfileResult,
    ] = await Promise.all([
      AppConfig.AppConfigModule.fetchById({ module: 'COMPANY', subModule: 'JOB' }) as Promise<CompanyJobConfigI>,
      EntityModule.fetchById(data.entity),
      DesignationModule.fetchById(data.designation),
      data.ship ? Ship.CoreShipModule.fetchByImo(data.ship) : null,
      data.isOfficial ? EntityMemberModule.isAnyMember({ entity: data.entity, profileId: selfProfileId }) : null,
      data.isOfficial ? EntityProfileModule.fetchByEntity(data.entity) : null,
    ]);

    const expiryDate = getDateOnly(data.expiryDate);
    JobModule.isExpiryDateValid({ appConfig, expiryDate, isUrgent: data.isUrgent });

    const input: Prisma.JobUncheckedCreateInput = {
      profileId: selfProfileId,
      expiryDate,
      minYears: data.minYears,

      jobType: data.jobType,
      genderDiversityIndex: data.genderDiversityIndex,
      joiningDate: data.joiningDate ? getDateOnly(data.joiningDate) : null,
      countryIso2: data.countryIso2,
      isUrgent: data.isUrgent || false,
      isOfficial: data.isOfficial,
      showShipDetails: data.showShipDetails ?? true,
      applicationMethod: data.applicationMethod ?? 'IN_APP',
      applicationEmail: data.applicationEmail || null,
      applicationUrl: data.applicationUrl || null,
    };

    if (data.isOfficial && entityProfileResult) {
      input.entityProfileId = entityProfileResult.id;
    }

    if (data.entity.dataType === 'master') {
      input.entityId = data.entity.id;
    } else {
      input.entityRawDataId = data.entity.id;
    }

    if (data.designation.dataType === 'master') {
      input.designationAlternativeId = data.designation.id;
    } else {
      input.designationRawDataId = data.designation.id;
    }

    if (data.ship) {
      if (data.ship.dataType === 'master') {
        input.shipImo = data.ship.imo;
      } else {
        input.shipRawDataImo = data.ship.imo;
      }
    }

    if (data.shipType) {
      if (data.shipType.dataType === 'master') {
        input.shipTypeId = data.shipType.id;
      } else {
        input.shipTypeRawDataId = data.shipType.id;
      }
    }

    if (data.maxYears) input.maxYears = data.maxYears;

    const result = await prismaPG.job.create({ data: input });
    return pick(result, ['id']);
  },

  updateJobDetails: async (state: FastifyStateI, data: Job.UpdateJobDetailsI, mode: 'draft' | 'edit' = 'draft') => {
    const selfProfileId = state.profileId;

    const existingJob = await prismaPG.job.findUnique({
      where: { id: data.jobId },
      select: {
        id: true,
        profileId: true,
        status: true,
      },
    });

    if (!existingJob) {
      throw new AppError('JOB008');
    }

    if (existingJob.profileId !== selfProfileId) {
      throw new AppError('JOB015');
    }

    if (mode === 'draft' && existingJob.status !== 'DRAFT') {
      throw new AppError('JOB016');
    }
    
    if (mode === 'edit' && existingJob.status !== 'ACTIVE') {
      throw new AppError('JOB023');
    }

    // Validate external dependencies if they are being updated
    const validationPromises = [];

    if (data.entity) {
      validationPromises.push(EntityModule.fetchById(data.entity));
    }

    if (data.designation) {
      validationPromises.push(DesignationModule.fetchById(data.designation));
    }

    if (data.ship) {
      validationPromises.push(Ship.CoreShipModule.fetchByImo(data.ship));
    }

    if (data.isOfficial && data.entity) {
      validationPromises.push(EntityMemberModule.isAnyMember({ entity: data.entity, profileId: selfProfileId }));
      validationPromises.push(EntityProfileModule.fetchByEntity(data.entity));
    }

    if (data.expiryDate) {
      const appConfig = await AppConfig.AppConfigModule.fetchById({ module: 'COMPANY', subModule: 'JOB' }) as CompanyJobConfigI;
      const expiryDate = getDateOnly(data.expiryDate);
      JobModule.isExpiryDateValid({ appConfig, expiryDate, isUrgent: data.isUrgent || false });
    }

    // Wait for all validations to complete
    if (validationPromises.length > 0) {
      await Promise.all(validationPromises);
    }

    // Build update input
    const input: Prisma.JobUncheckedUpdateInput = {};

    if (data.expiryDate) input.expiryDate = getDateOnly(data.expiryDate);
    if (data.minYears !== undefined) input.minYears = data.minYears;
    if (data.maxYears !== undefined) input.maxYears = data.maxYears;
    if (data.jobType) input.jobType = data.jobType;
    if (data.genderDiversityIndex !== undefined) input.genderDiversityIndex = data.genderDiversityIndex;
    if (data.joiningDate !== undefined) input.joiningDate = data.joiningDate ? getDateOnly(data.joiningDate) : null;
    if (data.countryIso2) input.countryIso2 = data.countryIso2;
    if (data.isUrgent !== undefined) input.isUrgent = data.isUrgent;
    if (data.isOfficial !== undefined) input.isOfficial = data.isOfficial;
    if (data.showShipDetails !== undefined) input.showShipDetails = data.showShipDetails;
    if (data.applicationMethod) input.applicationMethod = data.applicationMethod;
    if (data.applicationEmail !== undefined) input.applicationEmail = data.applicationEmail;
    if (data.applicationUrl !== undefined) input.applicationUrl = data.applicationUrl;

    if (data.entity) {
      if (data.entity.dataType === 'master') {
        input.entityId = data.entity.id;
        input.entityRawDataId = null;
      } else {
        input.entityId = null;
        input.entityRawDataId = data.entity.id;
      }
    }

    if (data.designation) {
      if (data.designation.dataType === 'master') {
        input.designationAlternativeId = data.designation.id;
        input.designationRawDataId = null;
      } else {
        input.designationAlternativeId = null;
        input.designationRawDataId = data.designation.id;
      }
    }

    if (data.ship !== undefined) {
      if (data.ship) {
        if (data.ship.dataType === 'master') {
          input.shipImo = data.ship.imo;
          input.shipRawDataImo = null;
        } else {
          input.shipImo = null;
          input.shipRawDataImo = data.ship.imo;
        }
      } else {
        input.shipImo = null;
        input.shipRawDataImo = null;
      }
    }

    if (data.shipType !== undefined) {
      if (data.shipType) {
        if (data.shipType.dataType === 'master') {
          input.shipTypeId = data.shipType.id;
          input.shipTypeRawDataId = null;
        } else {
          input.shipTypeId = null;
          input.shipTypeRawDataId = data.shipType.id;
        }
      } else {
        input.shipTypeId = null;
        input.shipTypeRawDataId = null;
      }
    }

    const result = await prismaPG.job.update({
      where: { id: data.jobId },
      data: input,
    });

    return pick(result, ['id']);
  },

  updateJobRequirements: async (state: FastifyStateI, data: Job.UpdateJobRequirementsI, mode: 'draft' | 'edit' = 'draft') => {
    const selfProfileId = state.profileId;

    return await prismaPG.$transaction(async (txn) => {
      const existingJob = await txn.job.findUnique({
        where: { id: data.jobId },
        select: {
          id: true,
          profileId: true,
          status: true,
        },
      });

      if (!existingJob) {
        throw new AppError('JOB008');
      }

      if (existingJob.profileId !== selfProfileId) {
        throw new AppError('JOB015');
      }

      if (mode === 'draft' && existingJob.status !== 'DRAFT') {
        throw new AppError('JOB016');
      }
      
      if (mode === 'edit' && existingJob.status !== 'ACTIVE') {
        throw new AppError('JOB020');
      }

      const jobInput: Prisma.JobUncheckedUpdateInput = {
        about: data.about,
        rolesResponsibilities: data.rolesResponsibilities,
        requirementType: data.requirementType,
        requirements: data.basicRequirements,
      };

      await txn.job.update({
        where: { id: data.jobId },
        data: jobInput,
      });

      if (data.certificationRequirements !== undefined) {
        if (mode === 'draft') {
          await txn.jobCertificationRequirement.deleteMany({ where: { jobId: data.jobId } });
          if (data.certificationRequirements.length > 0) {
            await txn.jobCertificationRequirement.createMany({
              data: data.certificationRequirements.map((req) => ({
                jobId: data.jobId,
                certificateCourseId: req.certification.dataType === 'master' ? req.certification.id : null,
                certificateCourseRawDataId: req.certification.dataType === 'raw' ? req.certification.id : null,
                isMandatory: req.isMandatory,
              })),
            });
          }
        } else {
          const existingRequirements = await txn.jobCertificationRequirement.findMany({
            where: { jobId: data.jobId },
            select: { id: true, certificateCourseId: true, certificateCourseRawDataId: true, isMandatory: true }
          });

          const existingMap = new Map(existingRequirements.map(req => [
            `${req.certificateCourseId || req.certificateCourseRawDataId}-${req.isMandatory}`, 
            req.id
          ]));

          const incomingMap = new Set(data.certificationRequirements.map(req => 
            `${req.certification.id}-${req.isMandatory}`
          ));

          const toDelete = existingRequirements.filter(existing => {
            const key = `${existing.certificateCourseId || existing.certificateCourseRawDataId}-${existing.isMandatory}`;
            return !incomingMap.has(key);
          });

          const toCreate = data.certificationRequirements.filter(req => {
            const key = `${req.certification.id}-${req.isMandatory}`;
            return !existingMap.has(key);
          });

          if (toDelete.length > 0) {
            await txn.jobCertificationRequirement.deleteMany({
              where: { id: { in: toDelete.map(req => req.id) } }
            });
          }

          if (toCreate.length > 0) {
            await txn.jobCertificationRequirement.createMany({
              data: toCreate.map((req) => ({
                jobId: data.jobId,
                certificateCourseId: req.certification.dataType === 'master' ? req.certification.id : null,
                certificateCourseRawDataId: req.certification.dataType === 'raw' ? req.certification.id : null,
                isMandatory: req.isMandatory,
              })),
            });
          }
        }
      }

      if (data.documentRequirements !== undefined) {
        if (mode === 'draft') {
          await txn.jobDocumentRequirement.deleteMany({ where: { jobId: data.jobId } });
          if (data.documentRequirements.length > 0) {
            await txn.jobDocumentRequirement.createMany({
              data: data.documentRequirements.map((req, index) => ({
                jobId: data.jobId,
                documentTypeId: req.documentType.dataType === 'master' ? req.documentType.id : null,
                documentTypeRawDataId: req.documentType.dataType === 'raw' ? req.documentType.id : null,
                countries: req.countries || [],
                isMandatory: req.isMandatory,
                description: req.description,
                sequence: index,
              }))
            });
          }
        } else {
          await txn.jobDocumentRequirement.deleteMany({ where: { jobId: data.jobId } });
          if (data.documentRequirements.length > 0) {
            await txn.jobDocumentRequirement.createMany({
              data: data.documentRequirements.map((req, index) => ({
                jobId: data.jobId,
                documentTypeId: req.documentType.dataType === 'master' ? req.documentType.id : null,
                documentTypeRawDataId: req.documentType.dataType === 'raw' ? req.documentType.id : null,
                countries: req.countries || [],
                isMandatory: req.isMandatory,
                description: req.description,
                sequence: index,
              }))
            });
          }
        }
      }

      if (data.experienceRequirements !== undefined) {
        if (mode === 'draft') {
          await txn.jobExperienceRequirement.deleteMany({ where: { jobId: data.jobId } });
          if (data.experienceRequirements.length > 0) {
            await txn.jobExperienceRequirement.createMany({
              data: data.experienceRequirements.map((req) => ({
                jobId: data.jobId,
                designationAlternativeId:
                  req.designation && req.designation.dataType === 'master' ? req.designation.id : null,
                designationRawDataId: req.designation && req.designation.dataType === 'raw' ? req.designation.id : null,
                mainVesselTypeId: req.shipType && req.shipType.dataType === 'master' ? req.shipType.id : null,
                mainVesselTypeRawDataId: req.shipType && req.shipType.dataType === 'raw' ? req.shipType.id : null,
                monthsOfExperience: req.monthsOfExperience,
                isMandatory: req.isMandatory,
                isTotal: req.isTotal,
              })),
            });
          }
        } else {
          await txn.jobExperienceRequirement.deleteMany({ where: { jobId: data.jobId } });
          if (data.experienceRequirements.length > 0) {
            await txn.jobExperienceRequirement.createMany({
              data: data.experienceRequirements.map((req) => ({
                jobId: data.jobId,
                designationAlternativeId:
                  req.designation && req.designation.dataType === 'master' ? req.designation.id : null,
                designationRawDataId: req.designation && req.designation.dataType === 'raw' ? req.designation.id : null,
                mainVesselTypeId: req.shipType && req.shipType.dataType === 'master' ? req.shipType.id : null,
                mainVesselTypeRawDataId: req.shipType && req.shipType.dataType === 'raw' ? req.shipType.id : null,
                monthsOfExperience: req.monthsOfExperience,
                isMandatory: req.isMandatory,
                isTotal: req.isTotal,
              })),
            });
          }
        }
      }

      if (data.skillRequirements !== undefined) {
        if (mode === 'draft') {
          await txn.jobSkillRequirement.deleteMany({ where: { jobId: data.jobId } });
          if (data.skillRequirements.length > 0) {
            await txn.jobSkillRequirement.createMany({
              data: data.skillRequirements.map((req) => ({
                jobId: data.jobId,
                skillId: req.skill.dataType === 'master' ? req.skill.id : null,
                skillRawDataId: req.skill.dataType === 'raw' ? req.skill.id : null,
                isMandatory: req.isMandatory,
              })),
            });
          }
        } else {
          const existingSkills = await txn.jobSkillRequirement.findMany({
            where: { jobId: data.jobId },
            select: { id: true, skillId: true, skillRawDataId: true, isMandatory: true }
          });

          const existingMap = new Map(existingSkills.map(skill => [
            `${skill.skillId || skill.skillRawDataId}-${skill.isMandatory}`, 
            skill.id
          ]));

          const incomingMap = new Set(data.skillRequirements.map(req => 
            `${req.skill.id}-${req.isMandatory}`
          ));

          const toDelete = existingSkills.filter(existing => {
            const key = `${existing.skillId || existing.skillRawDataId}-${existing.isMandatory}`;
            return !incomingMap.has(key);
          });

          const toCreate = data.skillRequirements.filter(req => {
            const key = `${req.skill.id}-${req.isMandatory}`;
            return !existingMap.has(key);
          });

          if (toDelete.length > 0) {
            await txn.jobSkillRequirement.deleteMany({
              where: { id: { in: toDelete.map(skill => skill.id) } }
            });
          }

          if (toCreate.length > 0) {
            await txn.jobSkillRequirement.createMany({
              data: toCreate.map((req) => ({
                jobId: data.jobId,
                skillId: req.skill.dataType === 'master' ? req.skill.id : null,
                skillRawDataId: req.skill.dataType === 'raw' ? req.skill.id : null,
                isMandatory: req.isMandatory,
              })),
            });
          }
        }
      }

      if (data.cargoRequirements !== undefined) {
        await txn.jobCargoRequirement.deleteMany({ where: { jobId: data.jobId } });
        if (data.cargoRequirements.length > 0) {
          await txn.jobCargoRequirement.createMany({
            data: data.cargoRequirements.map((req) => ({
              jobId: data.jobId,
              name: req.name,
              code: req.code,
              monthsOfExperience: req.monthsOfExperience,
              isMandatory: req.isMandatory,
            })),
          });
        }
      }

      if (data.otherRequirements !== undefined) {
        await txn.jobOtherRequirement.deleteMany({ where: { jobId: data.jobId } });
        if (data.otherRequirements.length > 0) {
          await txn.jobOtherRequirement.createMany({
            data: data.otherRequirements.map((req, index) => ({
              jobId: data.jobId,
              sequenceNumber: index + 1,
              details: req.details,
              isMandatory: req.isMandatory,
            })),
          });
        }
      }

      return { id: data.jobId };
    });
  },

  updateJobBenefits: async (state: FastifyStateI, data: Job.UpdateJobBenefitsI, mode: 'draft' | 'edit' = 'draft') => {
    const selfProfileId = state.profileId;

    return await prismaPG.$transaction(async (txn) => {
      const existingJob = await txn.job.findUnique({
        where: { id: data.jobId },
        select: {
          id: true,
          profileId: true,
          status: true,
          minSalary: true,
        },
      });

      if (!existingJob) {
        throw new AppError('JOB008');
      }

      if (existingJob.profileId !== selfProfileId) {
        throw new AppError('JOB015');
      }

      if (mode === 'draft' && existingJob.status !== 'DRAFT') {
        throw new AppError('JOB016');
      }
      
      if (mode === 'edit' && existingJob.status !== 'ACTIVE') {
        throw new AppError('JOB021');
      }

      const jobInput: Prisma.JobUncheckedUpdateInput = {
        salaryType: data.salaryType,
        currencyCode: data.currencyCode,
        minSalary: data.minSalary,
        maxSalary: data.maxSalary,
        showSalary: data.showSalary,
        benefits: data.basicBenefits,
        status: 'ACTIVE',
      };

      await txn.job.update({
        where: { id: data.jobId },
        data: jobInput,
      });

      const jobBenefit = await txn.jobBenefit.upsert({
        where: { jobId: data.jobId },
        create: {
          jobId: data.jobId,
          benefitType: data.benefitType,
          contractMonths: data.contractMonths,
          contractDays: data.contractDays,
          internetAvailable: data.internetAvailable,
          internetSpeed: data.internetSpeed,
          internetLimitPerDay: data.internetLimitPerDay,
          internetDetails: data.internetDetails,
          insuranceType: data.insuranceType,
          familyOnboard: data.familyOnboard,
          itfType: data.itfType,
        },
        update: {
          benefitType: data.benefitType,
          contractMonths: data.contractMonths,
          contractDays: data.contractDays,
          internetAvailable: data.internetAvailable,
          internetSpeed: data.internetSpeed,
          internetLimitPerDay: data.internetLimitPerDay,
          internetDetails: data.internetDetails,
          insuranceType: data.insuranceType,
          familyOnboard: data.familyOnboard,
          itfType: data.itfType,
        },
      });

      await txn.jobBenefitDetail.deleteMany({ where: { jobBenefitId: jobBenefit.id } });

      const allBenefitDetails = [];

      if (data.basicBenefits?.trim()) {
        allBenefitDetails.push({ details: data.basicBenefits.trim() });
      }

      if (data.benefitDetails?.length) {
        allBenefitDetails.push(...data.benefitDetails);
      }

      if (allBenefitDetails.length) {
        await txn.jobBenefitDetail.createMany({
          data: allBenefitDetails.map((benefit, index) => ({
            jobBenefitId: jobBenefit.id,
            sequenceNumber: index + 1,
            details: benefit.details,
          })),
        });
      }

      return { id: data.jobId };
    });
  },

  updateJobRequirementsMobile: async (state: FastifyStateI, data: Job.UpdateJobRequirementsMobileI, mode: 'draft' | 'edit' = 'draft') => {
    const selfProfileId = state.profileId;

    return await prismaPG.$transaction(async (txn) => {
      const existingJob = await txn.job.findUnique({
        where: { id: data.jobId },
        select: {
          id: true,
          profileId: true,
          status: true,
          minSalary: true,
        },
      });

      if (!existingJob) {
        throw new AppError('JOB008');
      }

      if (existingJob.profileId !== selfProfileId) {
        throw new AppError('JOB015');
      }

      if (mode === 'draft' && existingJob.status !== 'DRAFT') {
        throw new AppError('JOB016');
      }
      
      if (mode === 'edit' && existingJob.status !== 'ACTIVE') {
        throw new AppError('JOB022');
      }

      await txn.job.update({
        where: { id: data.jobId },
        data: {
          about: data.about,
          rolesResponsibilities: data.rolesResponsibilities,
          requirementType: 'BASIC',
          requirements: data.requirements,
          benefits: data.benefits,
          status: 'ACTIVE',
        },
      });

      return { id: data.jobId };
    });
  },

  fetchDraftJob: async (state: FastifyStateI, jobId: string) => {
    const selfProfileId = state.profileId;

    const job = await prismaPG.job.findUnique({
      where: { id: jobId },
      include: {
        JobCertificationRequirement: true,
        JobDocumentRequirement: {
          include: {
            DocumentType: true,
            DocumentTypeRawData: true,
          },
          orderBy: { sequence: 'asc' }
        },
        JobExperienceRequirement: true,
        JobSkillRequirement: true,
        JobCargoRequirement: true,
        JobOtherRequirement: {
          orderBy: { sequenceNumber: 'asc' },
        },
        JobBenefit: {
          include: {
            JobBenefitDetail: {
              orderBy: { sequenceNumber: 'asc' },
            },
          },
        },
      },
    });

    if (!job) {
      throw new AppError('JOB008');
    }

    if (job.profileId !== selfProfileId) {
      throw new AppError('JOB015');
    }

    if (job.status !== 'DRAFT') {
      throw new AppError('JOB016');
    }

    return job;
  },
  fetchFiltersForCandidate: async (
    state: FastifyStateI,
    { entity, isOfficial, status }: Job.FetchFiltersForCandidateI,
  ) => {
    const conditions: string[] = [];

    if (status) {
      conditions.push(`j.status = ${status}`);
    }
    if (entity) {
      if (entity.dataType === 'master') {
        conditions.push(`j."entityId" = ${entity.id}`);
      } else {
        conditions.push(`j."entityRawDataId" = ${entity.id}`);
      }
    }
    if (isOfficial !== undefined) {
      conditions.push(`j."isOfficial" = ${isOfficial}`);
    }

    const whereClause = conditions.length > 0 ? Prisma.sql`WHERE ${Prisma.join(conditions, ' AND ')}` : Prisma.empty;

    const [countryResult, designationsResult,internetResults, shipTypeResults] = await Promise.all([
      prismaPG.$queryRaw<IdLabelCountDataTypeI[]>`
        SELECT j."countryIso2" as id, c.name as label, COUNT(*) as count
        FROM company."Job" j
        LEFT JOIN master."Country" c ON j."countryIso2" = c.iso2
        ${whereClause}
        GROUP BY j."countryIso2", c.name
      `,
      prismaPG.$queryRaw<IdLabelCountDataTypeI[]>`
        SELECT
          COALESCE(da.name, dr.name) as label,
          COALESCE(j."designationAlternativeId", j."designationRawDataId") as id,
          COUNT(*) as count,
          CASE
            WHEN j."designationAlternativeId" IS NOT NULL THEN 'master'
            ELSE 'raw'
          END as "dataType"
        FROM company."Job" j
        LEFT JOIN company."DesignationAlternative" da
          ON j."designationAlternativeId" = da.id
        LEFT JOIN "rawData"."DesignationRawData" dr
          ON j."designationRawDataId" = dr.id
        ${whereClause}
        GROUP BY label, j."designationAlternativeId", j."designationRawDataId"
      `,
      prismaPG.$queryRaw<IdLabelCountDataTypeI[]>`
        SELECT
          CASE
            WHEN jb."internetAvailable" = false THEN '0'
            WHEN jb."internetLimitPerDay" < 200 THEN '<200'
            WHEN jb."internetLimitPerDay" BETWEEN 200 AND 500 THEN '200-500'
            WHEN jb."internetLimitPerDay" BETWEEN 501 AND 1000 THEN '500-1000'
            WHEN jb."internetLimitPerDay" > 1000 THEN '1000+'
            ELSE 'unknown'
          END as id,
          CASE
            WHEN jb."internetAvailable" = false THEN 'No Internet'
            WHEN jb."internetLimitPerDay" < 200 THEN '< 200MB'
            WHEN jb."internetLimitPerDay" BETWEEN 200 AND 500 THEN '200MB - 500MB'
            WHEN jb."internetLimitPerDay" BETWEEN 501 AND 1000 THEN '500MB - 1000MB'
            WHEN jb."internetLimitPerDay" > 1000 THEN '> 1000MB'
            ELSE 'Unknown'
          END as label,
          COUNT(*) as count
        FROM company."Job" j
        LEFT JOIN company."JobBenefit" jb ON j.id = jb."jobId"
        ${whereClause}
        GROUP BY
          jb."internetAvailable",
          jb."internetLimitPerDay",
          CASE
            WHEN jb."internetAvailable" = false THEN '0'
            WHEN jb."internetLimitPerDay" < 200 THEN '<200'
            WHEN jb."internetLimitPerDay" BETWEEN 200 AND 500 THEN '200-500'
            WHEN jb."internetLimitPerDay" BETWEEN 501 AND 1000 THEN '500-1000'
            WHEN jb."internetLimitPerDay" > 1000 THEN '1000+'
            ELSE 'unknown'
          END
      `,
      prismaPG.$queryRaw<IdLabelCountDataTypeI[]>`
        SELECT mvt.id as id, mvt.name as label, COUNT(*) as count
        FROM company."Job" j
        JOIN ship."Ship" s ON j."shipImo" = s.imo
        JOIN ship."MainVesselType" mvt ON s."mainVesselTypeId" = mvt.id
        ${whereClause}
        GROUP BY mvt.id, mvt.name
      `,
    ]);

    const filters: JobFiltersParamsI = {
      locations: [],
      designations: [],
      shipTypes: [],
      internetLimits: []
    };
    countryResult.map((country) => {
      filters.locations.push({
        id: country.id,
        label: country.label,
        count: Number(country.count),
      });
    });
    designationsResult.map((designation) => {
      filters.designations.push({
        id: designation.id,
        label: designation.label,
        count: Number(designation.count),
        dataType: designation.dataType,
      });
    });
    internetResults.map((item) => {
      filters.internetLimits.push({
        id:item.id,
        label:item.label,
        count: Number(item.count),
      })
    });
    shipTypeResults.map((shipType) => {
      filters.shipTypes.push({
        id: shipType.id,
        label: shipType.label,
        count: Number(shipType.count),
        dataType: 'master',
      });
    });
    return filters;
  },
  fetchFiltersForEntityMember: async (
    state: FastifyStateI,
    { entity, isOfficial, status }: Job.FetchFiltersForCandidateI,
  ) => {
    const { profileId: selfProfileId, entityProfileId: selfEntityProfileId } = state;
    const conditions: Prisma.Sql[] = [];

    if (isOfficial) {
      if (!entity) {
        throw new AppError('JOB012');
      }
      await EntityMemberModule.isAnyMember({ entity, profileId: selfProfileId });
      conditions.push(Prisma.sql`j."entityProfileId" = ${selfEntityProfileId}::uuid`);
    } else {
      conditions.push(Prisma.sql`j."profileId" = ${selfProfileId}::uuid`);
    }

    if (status) {
      conditions.push(Prisma.sql`j.status = ${status}`);
    }

    if (entity) {
      if (entity.dataType === 'master') {
        conditions.push(Prisma.sql`j."entityId" = ${entity.id}::uuid`);
      } else {
        conditions.push(Prisma.sql`j."entityRawDataId" = ${entity.id}::uuid`);
      }
    }

    const whereClause = conditions.length > 0 ? Prisma.sql`WHERE ${Prisma.join(conditions, ' AND ')}` : Prisma.empty;

    const [designationsResult, shipTypeResults] = await Promise.all([
      prismaPG.$queryRaw<IdLabelCountDataTypeI[]>`
        SELECT
          COALESCE(da.name, dr.name) as label,
          COALESCE(j."designationAlternativeId"::text, j."designationRawDataId"::text) as id,
          COUNT(*) as count,
          CASE
            WHEN j."designationAlternativeId" IS NOT NULL THEN 'master'
            ELSE 'raw'
          END as "dataType"
        FROM company."Job" j
        LEFT JOIN company."DesignationAlternative" da
          ON j."designationAlternativeId" = da.id
        LEFT JOIN "rawData"."DesignationRawData" dr
          ON j."designationRawDataId" = dr.id
        ${whereClause}
        GROUP BY label, j."designationAlternativeId", j."designationRawDataId"
      `,
      prismaPG.$queryRaw<IdLabelCountDataTypeI[]>`
        SELECT mvt.id::text as id, mvt.name as label, COUNT(*) as count
        FROM company."Job" j
        JOIN ship."Ship" s ON j."shipImo" = s.imo
        JOIN ship."MainVesselType" mvt ON s."mainVesselTypeId" = mvt.id
        ${whereClause}
        GROUP BY mvt.id, mvt.name
      `,
    ]);

    const filters: PostedJobsFiltersParamsI = {
      designations: [],
      shipTypes: [],
    };

    designationsResult.forEach((designation) => {
      filters.designations.push({
        id: designation.id,
        label: designation.label,
        count: Number(designation.count),
        dataType: designation.dataType,
      });
    });

    shipTypeResults.forEach((shipType) => {
      filters.shipTypes.push({
        id: shipType.id,
        label: shipType.label,
        count: Number(shipType.count),
        dataType: 'master',
      });
    });

    return filters;
  },
};
