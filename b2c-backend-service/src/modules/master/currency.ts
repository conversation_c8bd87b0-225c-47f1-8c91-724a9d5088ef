import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { UniqueCurrencyI } from '@interfaces/master/currency';
import { CurrencyOptionsFetchI } from '@schemas/master/currency';

export const CurrencyModule = {
  fetchUnique: async ({ 
    page, 
    pageSize, 
    search 
  }: CurrencyOptionsFetchI): Promise<{ data: UniqueCurrencyI[]; total: number }> => {
    try {
      if (search?.length) {
        const searchPattern = `%${search}%`;
        
        const [currenciesResult, totalResult] = await Promise.all([
          prismaPG.$queryRaw<UniqueCurrencyI[]>`
            SELECT DISTINCT ON (c.code) 
              c.code,
              c.name,
              c.symbol,
              c.numeric,
              c.decimal
            FROM "master"."Currency" c
            WHERE (c.code ILIKE ${searchPattern} OR c.name ILIKE ${searchPattern})
            ORDER BY c.code ASC, c.name ASC
            OFFSET ${page}
            LIMIT ${pageSize}
          `,
          prismaPG.$queryRaw<{ total: number }[]>`
            SELECT COUNT(DISTINCT c.code)::INTEGER AS total
            FROM "master"."Currency" c
            WHERE (c.code ILIKE ${searchPattern} OR c.name ILIKE ${searchPattern})
          `
        ]);

        return {
          data: currenciesResult,
          total: totalResult?.[0]?.total || 0,
        };
      } else {
        const [currenciesResult, totalResult] = await Promise.all([
          prismaPG.$queryRaw<UniqueCurrencyI[]>`
            SELECT DISTINCT ON (c.code) 
              c.code,
              c.name,
              c.symbol,
              c.numeric,
              c.decimal
            FROM "master"."Currency" c
            ORDER BY c.code ASC, c.name ASC
            OFFSET ${page}
            LIMIT ${pageSize}
          `,
          prismaPG.$queryRaw<{ total: number }[]>`
            SELECT COUNT(DISTINCT c.code)::INTEGER AS total
            FROM "master"."Currency" c
          `
        ]);

        return {
          data: currenciesResult,
          total: totalResult?.[0]?.total || 0,
        };
      }
    } catch (error) {
      console.error('Error fetching unique currencies:', error);
      throw new AppError('GEN001');
    }
  },

  fetchByCode: async (code: string): Promise<UniqueCurrencyI> => {
    try {
      const upperCode = code.toUpperCase();
      
      const currencyResult = await prismaPG.$queryRaw<UniqueCurrencyI[]>`
        SELECT DISTINCT ON (c.code)
          c.code,
          c.name,
          c.symbol,
          c.numeric,
          c.decimal
        FROM "master"."Currency" c
        WHERE c.code = ${upperCode}
        ORDER BY c.code ASC, c.name ASC
        LIMIT 1
      `;

      if (!currencyResult || currencyResult.length === 0) {
        throw new AppError('GEN002'); 
      }

      return currencyResult[0];
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      console.error('Error fetching currency by code:', error);
      throw new AppError('GEN001');
    }
  },

  fetchCodes: async (): Promise<string[]> => {
    try {
      const codesResult = await prismaPG.$queryRaw<{ code: string }[]>`
        SELECT DISTINCT c.code
        FROM "master"."Currency" c
        ORDER BY c.code ASC
      `;
      
      return codesResult.map(row => row.code);
    } catch (error) {
      console.error('Error fetching currency codes:', error);
      throw new AppError('GEN001');
    }
  }
};