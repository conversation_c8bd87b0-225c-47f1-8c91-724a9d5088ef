import { HttpStatus } from '@consts/common/api/status';

export type ErrorCodeValueI = {
  status: HttpStatus;
  message: string;
};

export const ErrorCodes = {
  //#region AddressRawData
  ADDR001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid address data',
  },
  ADDR002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the address',
  },
  ADDR003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Either city or cityMapbox is required',
  },
  //#endregion AddressRawData
  //#region Announcement
  ANC001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Either city or cityMapbox is required',
  },
  ANC002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'cityMapbox is required when addressMapBox is passed',
  },
  ANC003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Start date can be less than or equal to the end date.',
  },
  ANC004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Start date and end date can't be lesser than current date",
  },
  ANC005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the announcement',
  },
  ANC006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Payload is invalid',
  },
  ANC007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid announcement fetch parameters',
  },
  ANC008: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Not Authorized for this action',
  },
  //#endregion Announcement
  //#region Answer
  FMANS001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the answer',
  },
  FMANS002: {
    status: HttpStatus.NOT_FOUND,
    message: 'Answer not found',
  },
  FMANS003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to delete the answer',
  },
  FMANS004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'query is invalid',
  },
  FMANS005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'payload is invalid',
  },
  FMANS006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'route param is invalid',
  },
  FMANS007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "You've already update to this status",
  },
  FMANS008: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "You don't have permission to update the status",
  },
  FMANS009: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "You're not authorized to update this status",
  },
  //#endregion Answer
  //#region Answer Vote
  FMAVT001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to vote the answer',
  },
  FMAVT002: {
    status: HttpStatus.NOT_FOUND,
    message: 'Vote not found',
  },
  FMAVT003: {
    status: HttpStatus.CONFLICT,
    message: "You've already upvoted",
  },
  FMAVT004: {
    status: HttpStatus.CONFLICT,
    message: "You've already downvoted",
  },
  FMAVT005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to delete the vote',
  },
  FMAVT006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'query is invalid',
  },
  FMAVT007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'payload is invalid',
  },
  FMAVT008: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'route param is invalid',
  },
  //#endregion Answer
  //#region Answer Comment
  FMACMT001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to comment on the answer',
  },
  FMACMT002: {
    status: HttpStatus.NOT_FOUND,
    message: 'Comment not found',
  },
  FMACMT003: {
    status: HttpStatus.NOT_FOUND,
    message: 'Parent comment not found',
  },
  FMACMT004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'query is invalid',
  },
  FMACMT005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'payload is invalid',
  },
  FMACMT006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'route param is invalid',
  },
  FMACMT007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "You're not authorized to delete this comment",
  },
  //#endregion Answer Comment

  //#region AppConfig
  APPCFG001: {
    status: HttpStatus.NOT_FOUND,
    message: 'appConfig not found',
  },
  //#endregion AppConfig
  //#region AppVersion
  APPVN001: {
    status: HttpStatus.NOT_FOUND,
    message: 'App version not found',
  },
  APPVN002: {
    status: HttpStatus.NOT_FOUND,
    message: "App version isn't active",
  },
  //#endregion AppVersion
  //#region Auth
  AUTH001: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'Username or password is incorrect',
  },
  AUTH002: {
    status: HttpStatus.NOT_FOUND,
    message: 'User does not exist. Please sign up.',
  },
  AUTH003: {
    status: HttpStatus.FORBIDDEN,
    message: "You haven't created a password",
  },
  AUTH004: {
    status: HttpStatus.FORBIDDEN,
    message: "Profile's googleSub & token's user_id are different",
  },
  AUTH005: {
    status: HttpStatus.CONFLICT,
    message: 'Email is already registered',
  },
  AUTH006: {
    status: HttpStatus.CONFLICT,
    message: 'Username is already taken',
  },
  AUTH007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Data is missing',
  },
  AUTH008: {
    status: HttpStatus.BAD_REQUEST,
    message: 'Only EMAIL_PASSWORD type is allowed',
  },
  AUTH009: {
    status: HttpStatus.BAD_REQUEST,
    message: "platform isn't matching the appVersion's platform",
  },
  AUTH010: {
    status: HttpStatus.BAD_REQUEST,
    message: 'externalToken is invalid',
  },
  AUTH011: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'x-api-key is mandatory',
  },
  AUTH012: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'x-api-key is invalid',
  },
  AUTH013: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'x-platform is mandatory',
  },
  AUTH014: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'x-platform is invalid',
  },
  AUTH015: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'x-device-id is mandatory',
  },
  AUTH016: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'x-device-id is invalid',
  },
  AUTH017: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'x-version-no is mandatory',
  },
  AUTH018: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'x-version-no is invalid',
  },
  AUTH019: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'authorization token is mandatory',
  },
  AUTH020: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'authorization token is invalid',
  },
  AUTH021: {
    status: HttpStatus.UNAUTHORIZED,
    message: 'authorization token is expired',
  },
  AUTH022: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to logout',
  },
  AUTH023: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Sign in with Google',
  },
  AUTH024: {
    status: HttpStatus.CONFLICT,
    message: 'email is already verified',
  },
  AUTH025: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Payload is invalid',
  },
  AUTH026: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "You've exceeded the verification attempts. Try after sometime",
  },
  AUTH027: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to send the email. Try after sometime',
  },
  AUTH028: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Privacy policy/Terms of Service aren't accepted",
  },
  AUTH029: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Email verification required',
  },
  AUTH030: {
    status: HttpStatus.TOO_MANY_REQUESTS,
    message: 'Too many requests',
  },
  //#endregion Auth

  //#region Communication
  CMN001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'category is invalid',
  },
  CMN002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'topic is invalid',
  },
  CMN003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the communication',
  },
  CMN004: {
    status: HttpStatus.NOT_FOUND,
    message: 'Communication not found',
  },
  //#endregion Communication
  //#region Communication Template
  CMTMP001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Template not found',
  },
  //#endregion Communication Template
  //#region Communication Email
  CMEML001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Email's type is invalid",
  },
  CMEML002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to send the email',
  },
  CMEML003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Payload is invalid',
  },
  CMEML004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to verify the email',
  },
  //#endregion Communication Email
  //#region Communication Verification
  CMVFN001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to deactivate the previously communicated token/otp/link',
  },
  CMVFN002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the verification',
  },
  CMVFN003: {
    status: HttpStatus.NOT_FOUND,
    message: 'Verification not found',
  },
  CMVFN004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'otp is invalid',
  },
  //#endregion Communication Verification

  //#region Database
  DB001: {
    status: HttpStatus.CONFLICT,
    message: 'The data with the same identifier already exists',
  },
  DB002: {
    status: HttpStatus.NOT_FOUND,
    message: 'Data not found',
  },
  DB003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Referred data is invalid',
  },
  DB004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to complete the operation',
  },
  DB005: {
    status: 503,
    message: 'Database unavailable',
  },
  //#endregion Database

  //#region General
  GEN001: {
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    message: 'Something went wrong! Please try after sometime',
  },
  GEN002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid payload',
  },
  GEN003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Payload is empty',
  },
  GEN004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid parameter',
  },
  GEN005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid query',
  },
  GEN006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid data',
  },
  GEN007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid input data',
  },
  //#endregion General
  //#region Storage
  STRG001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to upload the file',
  },
  STRG002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "File type isn't allowed",
  },
  STRG003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Payload is invalid',
  },
  STRG004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to delete the file',
  },
  STRG005: {
    status: HttpStatus.NOT_FOUND,
    message: 'File not found for deletion',
  },
  //#endregion Storage
  //#region Vendor
  VND001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Vendor not found',
  },
  //#endregion Vendor
  //#region Internal service
  INSVC001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Internal service not found',
  },
  //#endregion Internal service
  //#region AppLog
  APPLG001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Failed to create the app's log",
  },
  APPLG002: {
    status: HttpStatus.NOT_FOUND,
    message: 'App log not found',
  },
  //#endregion AppLog
  //#region ServiceLog
  SVCLG001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Failed to create the service's log",
  },
  SVCLG002: {
    status: HttpStatus.NOT_FOUND,
    message: 'Service log not found',
  },
  //#endregion ServiceLog
  //#region Entity
  ENT001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Not authorized to add, update or delete',
  },
  ENT002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'only admin cannot demote themselves',
  },
  ENT003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Member not found',
  },
  ENT004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'only admin cannot delete',
  },
  ENT005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid query params',
  },
  ENT006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid body params',
  },
  ENT007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'You are not authorized to modify entity about data',
  },
  ENT008: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Request not found',
  },
  ENT009: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Not an admin',
  },
  ENT010: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Request Exist',
  },
  ENT011: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Already Approved',
  },
  ENT012: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Already existing',
  },
  ENT013: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Either entityId or entityRawDataId must be provided',
  },
  ENT014: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Admin limit of 5 exceeded',
  },
  ENT015: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Entity Profile not found',
  },
  ENT016: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Entity Profile not found',
  },
  ENT017: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "You've exceeded the verification attempts. Try after sometime",
  },
  ENT018: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to send the email. Try after sometime',
  },
  ENT019: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update organisation details.',
  },
  ENT020: {
    status: HttpStatus.UNAUTHORIZED,
    message: "You aren't authorized since you're not a member of the entity",
  },
  //#endregion Entity

  //#region Entity People
  ENPL001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'query is invalid',
  },
  //#endregion Entity People
  //#region Entity Profile
  ENPR001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Entity profile not found',
  },
  //#endregion Entity Profile
  //#region Entity Network
  ENTWRK001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'You are already following this user',
  },
  ENTWRK002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'You cannot follow yourself',
  },
  ENTWRK003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'You are not following this account',
  },
  ENTWRK004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to unfollow this account',
  },
  //#endregion Entity People
  //#region Department
  DEP001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Department not found',
  },
  DEP002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the department',
  },
  DEP003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the department',
  },
  DEP004: {
    status: HttpStatus.CONFLICT,
    message: 'Department already exists',
  },
  DEP005: {
    status: HttpStatus.BAD_REQUEST,
    message: 'Department validation error',
  },
  DEP006: {
    status: HttpStatus.BAD_REQUEST,
    message: 'Department search validation error',
  },
  //#endregion Department

  //#region Designation
  DSG001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Designation not found',
  },
  DSG002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the designation',
  },
  DSG003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the designation',
  },
  DSG004: {
    status: HttpStatus.CONFLICT,
    message: 'Designation already exists',
  },
  DSG005: {
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid designation data provided',
  },
  DSG006: {
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid designation search parameters',
  },
  //#endregion Designation

  //#region Entity
  ORG001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Entity not found',
  },
  ORG002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the entity',
  },
  ORG003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the entity',
  },
  ORG004: {
    status: HttpStatus.CONFLICT,
    message: 'Entity already exists',
  },
  //#endregion Entity
  //#region Experience
  EXP001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Experience not found',
  },
  EXP002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the experience',
  },
  EXP003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the experience',
  },
  EXP004: {
    status: HttpStatus.CONFLICT,
    message: 'Experience already exists',
  },
  EXP005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query is invalid',
  },
  EXP006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Payload is invalid',
  },
  EXP007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "fromDate can't be greater than toDate",
  },
  EXP008: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Capacity's unit isn't allowed for the selected ship type",
  },
  EXP009: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Update isn't allowed for the selected fuel type",
  },
  EXP010: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Refresh the experience's screen",
  },
  EXP011: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Param is invalid',
  },
  EXP012: {
    status: HttpStatus.NOT_FOUND,
    message: 'Equipment category not found',
  },
  EXP013: {
    status: HttpStatus.NOT_FOUND,
    message: 'Cargo not found',
  },
  EXP014: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'id is missing',
  },
  EXP015: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Max limit is of 8 skills',
  },
  //#endregion Experience

  //#region EquipmentCategory
  EQPCG001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Equipment category not found',
  },
  EQPCG002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the equipment category',
  },
  EQPCG003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the equipment category',
  },
  EQPCG004: {
    status: HttpStatus.CONFLICT,
    message: 'Equipment category already exists',
  },
  //#endregion EquipmentCategory
  //#region EquipmentManufacturer
  EQMNF001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Equipment manufacturer not found',
  },
  EQMNF002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the equipment manufacturer',
  },
  EQMNF003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the equipment manufacturer',
  },
  EQMNF004: {
    status: HttpStatus.CONFLICT,
    message: 'Equipment manufacturer already exists',
  },
  //#endregion EquipmentManufacturer
  //#region EquipmentModel
  EQMDL001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Equipment model not found',
  },
  EQMDL002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the equipment model',
  },
  EQMDL003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the equipment model',
  },
  EQMDL004: {
    status: HttpStatus.CONFLICT,
    message: 'Equipment model already exists',
  },
  //#endregion EquipmentModel

  //#region FuelType
  FULTYP001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Fuel type not found',
  },
  FULTYP002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the fuel type',
  },
  FULTYP003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the Fuel type',
  },
  FULTYP004: {
    status: HttpStatus.CONFLICT,
    message: 'Fuel type already exists',
  },
  //#endregion FuelType
  //#region ship
  SHIP001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Ship not found',
  },
  SHIP002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the Ship',
  },
  SHIP003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the Ship',
  },
  SHIP004: {
    status: HttpStatus.CONFLICT,
    message: 'Ship already exists',
  },
  SHIP005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query is invalid',
  },
  SHIP006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Payload is invalid',
  },
  //#endregion ship
  //#region MainVesselType
  MVSLTP001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Main Vessel type not found',
  },
  MVSLTP002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the main vessel type',
  },
  MVSLTP003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the main vessel type',
  },
  MVSLTP004: {
    status: HttpStatus.CONFLICT,
    message: 'Main Vessel type already exists',
  },
  MVSLTP005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Param is invalid',
  },
  MVSLTP006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query is invalid',
  },
  //#endregion MainVesselType
  //#region Job
  JOB001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Failed to create a ',
  },
  JOB002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Param is invalid',
  },
  JOB003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query is invalid',
  },
  JOB004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'An urgent job can expire only after 1 day',
  },
  JOB005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'A non urgent job can expire only after 10 days',
  },
  JOB006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'expiry date should be greater than or equal to the current date',
  },
  JOB007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'At least 1 attribute is required to be updated',
  },
  JOB008: {
    status: HttpStatus.NOT_FOUND,
    message: 'Job posting not found',
  },
  JOB009: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Payload is invalid',
  },
  JOB010: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Route param is invalid',
  },
  JOB011: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'minYears should be lesser than maxYears',
  },
  JOB012: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'If isOfficial is true then entity is mandatory',
  },
  JOB013: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "If isOfficial isn't present then then jobId is mandatory",
  },
  JOB014: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Job status must be DRAFT to perform this operation',
  },
  JOB015: {
    status: HttpStatus.FORBIDDEN,
    message: 'You do not have permission to modify this job',
  },
  JOB016: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Job is not in DRAFT status',
  },
  JOB017: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Email is required when application method is EMAIL',
  },
  JOB018: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'URL is required when application method is EXTERNAL_LINK',
  },
  JOB019: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Email and URL should not be provided when application method is IN_APP',
  },
  JOB020: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Job is not in ACTIVE status for editing requirements',
  },
  JOB021: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Job is not in ACTIVE status for editing benefits',
  },
  JOB022: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Job is not in ACTIVE status for editing mobile requirements',
  },
  JOB023: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Job is not in ACTIVE status for editing details',
  },
  //#endregion Job
  //#region Application
  APPL001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Application not found',
  },
  APPL002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'applicationId is mandatory while updating the application',
  },
  APPL003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'status was already updated to the requested status',
  },
  APPL004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "The requested status isn't allowed",
  },
  //#endregion Application
  //#region port
  PORT001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Port not found',
  },
  PORT002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the Port',
  },
  PORT003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the Port',
  },
  PORT004: {
    status: HttpStatus.CONFLICT,
    message: 'Port already exists',
  },
  PORT005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query params are invalid',
  },
  PORT006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Payload is invalid',
  },
  //#endregion port

  //#region Port visitor
  PRTVSR001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Visited status not found',
  },
  PRTVSR002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to mark the port as visited',
  },
  PRTVSR003: {
    status: HttpStatus.CONFLICT,
    message: "You've already marked this as visited",
  },
  PRTVSR004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query is invalid',
  },
  PRTVSR005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'id is required',
  },
  PRTVSR006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'payload is invalid',
  },
  PRTVSR007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to unmark the visited status',
  },
  //#endregion Port visitor
  //#region Port contribution
  PRTCNB001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Contribution not found',
  },
  PRTCNB002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the Contribution',
  },
  PRTCNB003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the Contribution',
  },
  PRTCNB004: {
    status: HttpStatus.CONFLICT,
    message: 'Contribution already exists',
  },
  PRTCNB005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query params are invalid',
  },
  PRTCNB006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create/update the Contribution',
  },
  PRTCNB007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Payload is invalid',
  },
  PRTCNB008: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'File is invalid',
  },
  PRTCNB009: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'At least one file is mandatory',
  },
  //#endregion Port contribution
  //#region Post
  POST001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Post not found',
  },
  POST002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the post',
  },
  POST003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the post',
  },
  POST004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to fetch the post',
  },
  POST005: {
    status: HttpStatus.FORBIDDEN,
    message: 'Unauthorized to modify this post',
  },
  POST006: {
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid post ID',
  },
  POST007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to delete the post',
  },
  POST008: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Payload's data is missing",
  },
  POST009: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Query's data is missing",
  },
  POST020: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Data is invalid',
  },
  POST021: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'id is invalid',
  },
  POST022: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid post ID format',
  },
  POST023: {
    message: 'Post exists but is not active',
    status: HttpStatus.CONFLICT,
  },
  //#endregion Post

  //#region PostMedia
  PMED001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Post media not found',
  },
  PMED002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to upload post media',
  },
  PMED003: {
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid media type',
  },
  //#endregion PostMedia

  //#region Profile
  PFL001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Profile not found',
  },
  PFL002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the profile',
  },
  PFL003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the profile',
  },
  PFL004: {
    status: HttpStatus.CONFLICT,
    message: 'Profile already exists',
  },
  PFL005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Designation is mandatory',
  },
  PFL006: {
    status: HttpStatus.CONFLICT,
    message: 'username is already used',
  },
  PFL007: {
    status: HttpStatus.CONFLICT,
    message: 'email is already used',
  },
  PFL008: {
    status: HttpStatus.CONFLICT,
    message: 'username is mandatory',
  },
  PFL009: {
    status: HttpStatus.CONFLICT,
    message: 'email is mandatory',
  },
  PFL010: {
    status: HttpStatus.CONFLICT,
    message: 'payload is missing data',
  },
  PFL011: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'id is missing',
  },
  PFL012: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to fetch profile data due to server error',
  },
  PFL013: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Profile already submitted for deletion',
  },
  PFL014: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Profile already deleted',
  },
  PFL015: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Your profile isn't active",
  },
  PFL016: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'id is missing',
  },
  //#endregion Profile

  //#region ProfileStatus
  PFSTS001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Profile status not found',
  },
  PFSTS002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the profile status',
  },
  PFSTS003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the profile status',
  },
  PFSTS004: {
    status: HttpStatus.CONFLICT,
    message: 'Profile status already exists',
  },
  //#endregion ProfileStatus
  //#region ProfileDepartment
  PFDEP001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Department not found',
  },
  PFDEP002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the department',
  },
  PFDEP003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the department',
  },
  PFDEP004: {
    status: HttpStatus.CONFLICT,
    message: 'Department already exists',
  },
  //#endregion ProfileDepartment

  //#region ProfileDesignation
  PFDSG001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Designation not found',
  },
  PFDSG002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the designation',
  },
  PFDSG003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the designation',
  },
  PFDSG004: {
    status: HttpStatus.CONFLICT,
    message: 'Designation already exists',
  },
  //#endregion ProfileDesignation

  //#region ProfileEducation
  PFEDU001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Education not found',
  },
  PFEDU002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the education',
  },
  PFEDU003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the education',
  },
  PFEDU004: {
    status: HttpStatus.CONFLICT,
    message: 'education already exists',
  },
  PFEDU005: {
    status: HttpStatus.NOT_FOUND,
    message: "One/more skills weren't found",
  },
  PFEDU006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query params are invalid',
  },
  PFEDU007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'id is required',
  },
  PFEDU008: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'payload is invalid',
  },
  PFEDU009: {
    status: HttpStatus.CONFLICT,
    message: 'payload is missing data',
  },
  //#endregion ProfileEducation
  //#region ProfileCertificate
  PFCRT001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Certificate not found',
  },
  PFCRT002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the Certificate',
  },
  PFCRT003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the Certificate',
  },
  PFCRT004: {
    status: HttpStatus.CONFLICT,
    message: 'Certificate already exists',
  },
  PFCRT005: {
    status: HttpStatus.NOT_FOUND,
    message: "One/more skills weren't found",
  },
  PFCRT006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query params are invalid',
  },
  PFCRT007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Payload is invalid',
  },
  PFCRT008: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'File is invalid',
  },
  PFCRT009: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Route params are invalid',
  },
  PFCRT010: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to delete the Certificate',
  },
  //#endregion ProfileCertificate

  //#region ProfileSkill
  PFSKL001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Skill not found',
  },
  PFSKL002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the skill',
  },
  PFSKL003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the skill',
  },
  PFSKL004: {
    status: HttpStatus.CONFLICT,
    message: 'Skill already exists',
  },
  PFSKL005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'At lease one skill is required',
  },
  PFSKL006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query params are invalid',
  },
  PFSKL007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'id is required',
  },
  PFSKL008: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'payload is invalid',
  },
  PFSKL009: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to delete the skill',
  },
  //#endregion ProfileSkill

  //#region Ship contribution
  SHPCNB001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Contribution not found',
  },
  SHPCNB002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the Contribution',
  },
  SHPCNB003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the Contribution',
  },
  SHPCNB004: {
    status: HttpStatus.CONFLICT,
    message: 'Contribution already exists',
  },
  SHPCNB005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query params are invalid',
  },
  SHPCNB006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create/update the Contribution',
  },
  SHPCNB007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Payload is invalid',
  },
  SHPCNB008: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'File is invalid',
  },
  SHPCNB009: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'At least one file is mandatory',
  },
  //#endregion Ship contribution

  //#region ScrapBook post
  SCBKPST001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Post not found',
  },
  SCBKPST002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the post',
  },
  SCBKPST003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the post',
  },
  SCBKPST004: {
    status: HttpStatus.CONFLICT,
    message: 'Post already exists',
  },
  SCBKPST005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query params are invalid',
  },
  SCBKPST006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'id is required',
  },
  SCBKPST007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'payload is invalid',
  },
  SCBKPST008: {
    status: HttpStatus.FORBIDDEN,
    message: "You aren't allowed to delete the post",
  },
  SCBKPST009: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to delete the post',
  },

  //#endregion ScrapBook post
  //#region ScrapBook reaction
  SCBKRCN001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Reaction not found',
  },
  SCBKRCN002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the reaction',
  },
  SCBKRCN003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the reaction',
  },
  SCBKRCN004: {
    status: HttpStatus.CONFLICT,
    message: 'Reaction already exists',
  },
  SCBKRCN005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query params are invalid',
  },
  SCBKRCN006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'id is required',
  },
  SCBKRCN007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'payload is invalid',
  },
  SCBKRCN008: {
    status: HttpStatus.FORBIDDEN,
    message: "You aren't allowed to delete the reaction",
  },
  SCBKRCN009: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to delete the reaction',
  },
  SCBKRCN010: {
    status: HttpStatus.FORBIDDEN,
    message: "You aren't allowed to create/update the reaction",
  },
  SCBKRCN011: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'ScrapBook post not found',
  },
  //#endregion ScrapBook reaction

  //#region ScrapBook comment
  SCBKCMT001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Comment not found',
  },
  SCBKCMT002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the comment',
  },
  SCBKCMT003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the comment',
  },
  SCBKCMT004: {
    status: HttpStatus.CONFLICT,
    message: 'Comment already exists',
  },
  SCBKCMT005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query params are invalid',
  },
  SCBKCMT007: {
    status: HttpStatus.NOT_FOUND,
    message: 'Parent comment not found',
  },
  SCBKCMT008: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'payload is invalid',
  },
  SCBKCMT009: {
    status: HttpStatus.FORBIDDEN,
    message: "You aren't allowed to delete the comment",
  },
  SCBKCMT010: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to delete the comment',
  },
  //#endregion ScrapBook comment
  //#region ShipType
  SHPTYP001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Ship type not found',
  },
  SHPTYP002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the ship type',
  },
  SHPTYP003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the ship type',
  },
  SHPTYP004: {
    status: HttpStatus.CONFLICT,
    message: 'Ship type already exists',
  },
  //#endregion ShipType

  //#region Reaction
  PRCT001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Reaction not found',
  },
  PRCT002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to add reaction to the post',
  },
  PRCT003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to remove the reaction',
  },
  PRCT004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Payload's data is missing",
  },
  PRCT005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Query's data is missing",
  },
  PRCT006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Query's data is invalid",
  },
  PRCT007: {
    status: HttpStatus.CONFLICT,
    message: "You've already given this reaction",
  },
  //#endregion Reaction

  //#region Comment
  PCMT001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Comment not found',
  },
  PCMT002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the comment',
  },
  PCMT003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the comment',
  },
  PCMT004: {
    status: HttpStatus.NOT_FOUND,
    message: 'No comments found',
  },
  PCMT005: {
    status: HttpStatus.FORBIDDEN,
    message: 'Unauthorized to modify this comment',
  },
  PCMT006: {
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid comment ID',
  },
  PCMT007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to delete the comment',
  },
  PCMT008: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Failed to update the comment's reaction",
  },
  PCMT009: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Payload's data is invalid",
  },
  PCMT010: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'id is mandatory',
  },
  PCMT011: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Nested reply isn't allowed",
  },
  PCMT012: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Query's data is invalid",
  },
  //#endregion Comment
  //#region People

  PPL001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query is invalid',
  },
  //#endregion People
  //#region Request
  REQ001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Request not found',
  },
  REQ002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to send request',
  },
  REQ003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to accept request',
  },
  REQ004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to reject request',
  },
  REQ005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to revoke request',
  },
  REQ006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to block user',
  },
  REQ007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to disconnect user',
  },
  REQ008: {
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid request data',
  },
  REQ009: {
    status: HttpStatus.NOT_FOUND,
    message: 'Profile not found',
  },
  REQ010: {
    status: HttpStatus.CONFLICT,
    message: 'Request was already sent',
  },
  REQ011: {
    status: HttpStatus.CONFLICT,
    message: 'You both are already connected',
  },
  REQ012: {
    status: HttpStatus.CONFLICT,
    message: "You've already received a request from this user",
  },
  REQ013: {
    status: HttpStatus.CONFLICT,
    message: "You've already sent a request from this user",
  },
  REQ014: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Requested status is invalid',
  },
  REQ015: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Payload is invalid',
  },
  REQ016: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query is invalid',
  },
  REQ017: {
    status: HttpStatus.CONFLICT,
    message: 'Request was already updated',
  },
  //#endregion Request

  //#region Connection
  CN001: {
    status: HttpStatus.NOT_FOUND,
    message: "You're not connected",
  },
  CN002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query is invalid',
  },
  CN003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'profileId is invalid',
  },
  CN004: {
    status: HttpStatus.BAD_REQUEST,
    message: 'Invalid connection data',
  },
  CN005: {
    status: HttpStatus.NOT_FOUND,
    message: 'Profile not found',
  },
  //#endregion Connection

  //#region BlockedProfile
  BLOCK001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Profile not found',
  },
  BLOCK002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to block the profile',
  },
  BLOCK003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to unblock the profile',
  },
  BLOCK004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to fetch the blocked profiles',
  },
  BLOCK005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query is invalid',
  },
  BLOCK006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Payload is invalid',
  },
  //#endregion BlockedProfile

  //#region Follow
  FLW001: {
    status: HttpStatus.NOT_FOUND,
    message: "Follow's record not found",
  },
  FLW002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to follow the user',
  },
  FLW003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to unfollow the user',
  },
  FLW004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid follow request data',
  },
  FLW005: {
    status: HttpStatus.CONFLICT,
    message: "You're already following this user",
  },
  FLW006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to unfollow this user',
  },
  FLW007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Payload is invalid',
  },
  FLW008: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query is invalid',
  },
  //#endregion Follow

  //#region Degree
  DGR001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Degree not found',
  },
  DGR002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the Degree',
  },
  DGR003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the Degree',
  },
  DGR004: {
    status: HttpStatus.CONFLICT,
    message: 'Degree already exists',
  },
  //#endregion Degree
  //#region Certificate Course
  CRTCR001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Certificate Course not found',
  },
  CRTCR002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the Certificate Course',
  },
  CRTCR003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the Certificate Course',
  },
  CRTCR004: {
    status: HttpStatus.CONFLICT,
    message: 'Certificate Course already exists',
  },
  CRTCR005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query is invalid',
  },
  CRTCR006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Payload is invalid',
  },
  //#endregion Certificate Course

  //#region Skill
  SKL001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Skill not found',
  },
  SKL002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the Skill',
  },
  SKL003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the Skill',
  },
  SKL004: {
    status: HttpStatus.CONFLICT,
    message: 'Skill already exists',
  },
  SKL005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Payload is invalid',
  },
  SKL006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query is invalid',
  },
  //#endregion Skill
  //#region Document
  DOC001: {
    status: HttpStatus.NOT_FOUND,
    message: "Document type not found"
  },
  DOC002: {
    status: HttpStatus.CONFLICT,
    message: "Document type already exists"
  },
  DOC003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Cannot update approved document type "
  },
  DOC004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Invalid query parameters"
  },
  DOC005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Invalid request body for document type creation"
  },
  DOC006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Invalid route parameters'"
  },
  DOC007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Invalid request body for document type update"
  },
  //#endregion Document
  //#region Identity
  IDTY001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Identity document not found',
  },
  IDTY002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the Identity document',
  },
  IDTY003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the Identity document',
  },
  IDTY004: {
    status: HttpStatus.CONFLICT,
    message: 'Identity document already exists',
  },
  IDTY005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Payload is invalid',
  },
  IDTY006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'File is invalid',
  },
  IDTY007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query is invalid',
  },
  IDTY008: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "fromDate can't be greater than untilDate",
  },
  IDTY009: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'id is mandatory',
  },
  IDTY010: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to delete the Identity document',
  },
  IDTY011: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Params is invalid',
  },
  //#endregion Identity
  //#region Visa
  VISA001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Visa document not found',
  },
  VISA002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the Visa document',
  },
  VISA003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the Visa document',
  },
  VISA004: {
    status: HttpStatus.CONFLICT,
    message: 'Visa document already exists',
  },
  VISA005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Payload is invalid',
  },
  VISA006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'File is invalid',
  },
  VISA007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query params are invalid',
  },
  VISA008: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "fromDate can't be greater than untilDate",
  },
  VISA009: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'id is mandatory',
  },
  VISA010: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to delete the Visa document',
  },
  //#endregion Visa
  //#region City
  CITY001: {
    status: HttpStatus.NOT_FOUND,
    message: 'City not found',
  },
  CITY002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the city',
  },
  CITY003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the city',
  },
  CITY004: {
    status: HttpStatus.CONFLICT,
    message: 'City already exists',
  },
  CITY005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Query params are invalid',
  },
  CITY006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Payload is invalid',
  },
  CITY007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid city location data',
  },
  //#endregion City
  //#region Country
  CNRY001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Country not found',
  },
  CNRY002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the Country',
  },
  CNRY003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the Country',
  },
  CNRY004: {
    status: HttpStatus.CONFLICT,
    message: 'Country already exists',
  },
  //#endregion Country
  //#region Notification
  NF001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Notification's data is invalid",
  },
  NF002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Like notification's data is invalid",
  },
  NF003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Comment notification's data is invalid",
  },
  NF004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Reply notification's data is invalid",
  },
  NF005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Public notification's data is invalid",
  },
  NF006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Message notification's data is invalid",
  },
  NF007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "Notification's type is invalid",
  },
  NF008: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create a notification',
  },
  NF009: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update a notification',
  },
  NF010: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'query is invalid',
  },
  NF011: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'payload is invalid',
  },
  NF012: {
    status: HttpStatus.NOT_FOUND,
    message: 'Notification not found',
  },
  NF013: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'topic is invalid',
  },
  NF014: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'The data for question notification is invalid',
  },
  NF015: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'The data for question vote notification is invalid',
  },
  NF016: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'The data for answer vote notification is invalid',
  },
  NF017: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'The data for question comment notification is invalid',
  },
  NF018: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'The data for question reply notification is invalid',
  },
  NF019: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'The data for answer comment notification is invalid',
  },
  NF020: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'The data for answer reply notification is invalid',
  },
  NF021: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'The data for answer verified notification is invalid',
  },
  NF022: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'The data for answer notification is invalid',
  },
  NF023: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'The data for questions notification is invalid',
  },
  //#endregion Notification
  //#region Community
  CMTY001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the community',
  },
  CMTY002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to Update Community',
  },
  CMTY003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to delete the community',
  },
  CMTY004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Not Authorised to Update Community',
  },
  CMTY005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Cannot Delete Without Appointing another Admin',
  },
  CMTY006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid query parameters for fetching communities',
  },
  CMTY007: {
    status: HttpStatus.NOT_FOUND,
    message: 'Community not found',
  },
  //#endregion Community
  //#region Member
  FMMB001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Member not found',
  },
  FMMB002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Not Authorised for this action',
  },
  FMMB003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Only admin cannot leave the community',
  },
  FMMB004: {
    status: HttpStatus.NOT_FOUND,
    message: 'Community not found',
  },
  FMMB005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'requestedType is invalied',
  },
  FMMB006: {
    status: HttpStatus.NOT_FOUND,
    message: 'Member not found',
  },
  FMMB007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "You're not allowed to create a question",
  },
  //#endregion Member
  //#region CommunityRequest
  CMRQ001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create Community Request ',
  },
  CMRQ002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: "You've already sent a request to join the community",
  },
  CMRQ003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Already a member of this community',
  },
  CMRQ004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Request not found or already processed',
  },
  CMRQ005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'payload is invalid',
  },
  //#endregion CommunityRequest

  //#region QuestionComment
  FQCM001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Question not found ',
  },
  FQCM002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create comment',
  },
  FQCM003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Comment not found',
  },
  FQCM004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Not authorized to update this comment',
  },
  FQCM005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'No update in comment',
  },
  FQCM006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Comment update failed',
  },
  FQCM007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Not authorized to delete this comment',
  },
  FQCM008: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: ' Failed to delete comment',
  },

  FQCM009: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid parent comment',
  },
  FQCM010: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Unauthorized access',
  },
  //#endregion QuestionComment
  //#region Question
  FMQUE001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'A question cannot have more than 3 topics',
  },
  FMQUE002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'equipmentCategory/equipmentManufacturer/equipmentModel are required',
  },
  FMQUE003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the question',
  },
  FMQUE004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Not Authorised to post Questions',
  },
  FMQUE005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Data is invalid',
  },
  FMQUE006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Not Authorised for this action',
  },
  FMQUE007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to delete Question',
  },
  FMQUE008: {
    status: HttpStatus.NOT_FOUND,
    message: 'Question not found',
  },
  FMQUE009: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'query is invalid',
  },
  FMQUE010: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Payload is invalid',
  },
  FMQUE011: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Route param is invalid',
  },
  FMQUE012: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'At least 1 topic is required',
  },
  FMQUE013: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'The question is already live',
  },
  FMQUE014: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'The question is already inactive',
  },
  FMQUE015: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the question',
  },
  //#endregion Question
  //#region Question Vote
  FMQVT001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to vote the question',
  },
  FMQVT002: {
    status: HttpStatus.NOT_FOUND,
    message: 'Vote not found',
  },
  FMQVT003: {
    status: HttpStatus.CONFLICT,
    message: "You've already upvoted",
  },
  FMQVT004: {
    status: HttpStatus.CONFLICT,
    message: "You've already downvoted",
  },
  FMQVT005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to delete the vote',
  },
  FMQVT006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'query is invalid',
  },
  FMQVT007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'payload is invalid',
  },
  FMQVT008: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'route param is invalid',
  },
  //#endregion Question

  //#region Topic
  FMTP001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Topic Not Found',
  },
  FMTP002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the topic',
  },
  //#endregion Topic
  //#region Privacy Policy
  PP001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Privacy Policy  Not Found',
  },

  //#endregion Privacy Policy
  //#region Reward
  RWD001: {
    status: HttpStatus.NOT_FOUND,
    message: 'Reward  Not Found',
  },
  RWD002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Nothing to unassign',
  },
  RWD003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Score cannot go below zero',
  },
  RWD004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid  request body',
  },
  RWD005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid score type provided',
  },
  RWD006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid  query params',
  },
  //#endregion Reward
  //#region Leaderboard
  LBD001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid leaderboard duration specified',
  },
  LBD002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid leaderboard type specified',
  },
  LBD003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid weekly leaderboard type',
  },
  LBD004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid overall leaderboard type',
  },
  LBD005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid leaderboard query params',
  },
  //#endregion Leaderboard
  //#region Referral
  REF001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid referral code request data',
  },
  REF002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create referral status',
  },
  REF003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Referred profile ID is required',
  },
  REF004: {
    status: HttpStatus.NOT_FOUND,
    message: 'Referral code not found',
  },
  REF005: {
    status: HttpStatus.CONFLICT,
    message: 'Referral code already exists for this profile',
  },
  REF006: {
    status: HttpStatus.CONFLICT,
    message: 'Referral status already exists for this referred profile',
  },
  REF007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to mark onboarding as complete',
  },
  REF008: {
    status: HttpStatus.NOT_FOUND,
    message: 'Referral status not found for the given profile',
  },
  //#endregion Referral
  //#region RSVP
  RSVP001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Attendance already confirmed',
  },
  RSVP002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Announcement does not exist',
  },
  RSVP003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid request body',
  },
  RSVP004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'RSVP not found',
  },
  RSVP005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Cannot delete attendance for your own announcement',
  },
  //#endregion RSVP
  //#region Session
  SESS001: {
    status: HttpStatus.TOO_MANY_REQUESTS,
    message: 'Maximum session limit exceeded',
  },
  SESS002: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Payload is invalid',
  },
  SESS003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to delsert the session',
  },
  SESS004: {
    status: HttpStatus.CONFLICT,
    message: 'Session already exists',
  },
  SESS005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the session',
  },
  SESS006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to update the session',
  },
  SESS007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to delete the session',
  },
  SESS008: {
    status: HttpStatus.NOT_FOUND,
    message: 'Session not found',
  },
  //#endregion Session
  //region News
  NEWS001: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to fetch news topics',
  },
  NEWS002: {
    status: HttpStatus.NOT_FOUND,
    message: 'News topic not found',
  },
  NEWS003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to fetch news articles',
  },
  NEWS004: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid news query parameters',
  },
  NEWS005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid news topics query parameters',
  },
  //#endregion News
  //#region SavedPost
  SAVEDPOST001: {
    status: HttpStatus.CONFLICT,
    message: 'Post is already saved',
  },
  SAVEDPOST002: {
    status: HttpStatus.NOT_FOUND,
    message: 'Post not found',
  },
  SAVEDPOST003: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Failed to create the saved post',
  },
  SAVEDPOST004: {
    status: HttpStatus.NOT_FOUND,
    message: 'Saved post not found',
  },
  SAVEDPOST005: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid request body for creating saved post',
  },
  SAVEDPOST006: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid request body for deleting saved post',
  },
  SAVEDPOST007: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid query parameters for fetching saved posts',
  },
  SAVEDPOST008: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid pagination data for fetching saved posts',
  },
  SAVEDPOST009: {
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    message: 'Invalid route parameters for saved post check',
  },
  //#endregion SavedPost
};

export type ErrorCodeI = keyof typeof ErrorCodes;
