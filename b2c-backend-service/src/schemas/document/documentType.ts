import { DBDataTypeE } from "@consts/common/data";
import { z } from 'zod';
import { DocumentCategoryE } from '@prisma/postgres'
import { PaginationSchema } from "@schemas/common/common";
import { UUIDSchema } from "@navicater/b2c-internal-communication";

export const DocumentTypeCreateSchema = z.object({
    name: z.string().min(2).max(150),
    category: z.enum([DocumentCategoryE.IDENTITY, DocumentCategoryE.VISA]),
});

export type DocumentTypeCreateI = z.infer<typeof DocumentTypeCreateSchema>;


export const DocumentTypeUpdateSchema = z.object({
    name: z.string().min(2).max(150),
});

export type DocumentTypeUpdateI = z.infer<typeof DocumentTypeUpdateSchema>;


export const DocumentTypeOptionsFetchSchema = PaginationSchema.extend({
    search: z.string().min(1).max(150),
});

export type DocumentTypeOptionsFetchI = z.infer<typeof DocumentTypeOptionsFetchSchema>;

export const DocumentTypeAllOptionsFetchSchema = PaginationSchema.extend({
    search: z.string().min(1).max(150).optional(),
});

export type DocumentTypeAllOptionsFetchI = z.infer<typeof DocumentTypeAllOptionsFetchSchema>;

export const DocumentTypeFetchForExternalClientSchema = z.object({
    id: z.string(UUIDSchema),
    dataType: DBDataTypeE,
});

export type DocumentTypeFetchForExternalClientI = z.infer<typeof DocumentTypeFetchForExternalClientSchema>;
