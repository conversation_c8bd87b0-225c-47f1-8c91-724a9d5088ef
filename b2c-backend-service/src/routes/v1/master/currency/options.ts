import AppError from '@classes/AppError';
import { HttpStatus } from '@consts/common/api/status';
import Master from '@modules/master';
import { CurrencyOptionsFetchSchema } from '@schemas/master/currency';
import type { CurrencyOptionsFetchI } from '@schemas/master/currency';
import type { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

const currencyRoutes = (fastify: FastifyInstance): void => {
  /**
   * GET /backend/api/v1/master/currency/options
   * Fetch unique currencies with pagination and search
   */
  fastify.get('/backend/api/v1/master/currency/options', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const { error, data } = CurrencyOptionsFetchSchema.safeParse(request.query);

    if (error) {
      throw new AppError('GEN005', error);
    }

    const currenciesResult = await Master.CurrencyModule.fetchUnique(data as CurrencyOptionsFetchI);
    reply.status(HttpStatus.OK).send(currenciesResult);
  });

  /**
   * GET /backend/api/v1/master/currency/codes
   * Fetch all currency codes
   */
  fastify.get('/backend/api/v1/master/currency/codes', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const codesResult = await Master.CurrencyModule.fetchCodes();
    reply.status(HttpStatus.OK).send({ data: codesResult });
  });

  /**
   * GET /backend/api/v1/master/currency/:code
   * Fetch single currency by code
   */
  fastify.get<{ Params: { code: string } }>('/backend/api/v1/master/currency/:code', {}, async (request: FastifyRequest, reply: FastifyReply) => {
    const { code } = request.params as { code: string };

    if (!code || code.length < 2 || code.length > 5) {
      throw new AppError('GEN004');
    }

    const currencyResult = await Master.CurrencyModule.fetchByCode(code);
    reply.status(HttpStatus.OK).send({ data: currencyResult });
  });
};

export default currencyRoutes;