import type { DocumentCategoryI } from "@consts/document/documentType";
import type { DBDataTypeI } from "@consts/common/data";
// import type { StatusRawData } from "@prisma/postgres";

export interface DocumentTypeClientI {
    id: string;
    name: string;
    category: DocumentCategoryI;
    dataType: DBDataTypeI;
}

export interface DocumentTypeCreateParamsI {
    name: string;
    category: DocumentCategoryI;
}

export interface DocumentTypeUpdateParamsI {
    name?: string;
}
