import { apiCall } from '@/lib/api';
import { FetchJobsQueryI, FetchJobsResponseI } from './types';

export const fetchJobsForCandidateAPI = async (
  query: FetchJobsQueryI
): Promise<FetchJobsResponseI> => {
  const result = await apiCall<FetchJobsQueryI, FetchJobsResponseI>(
    '/backend/api/v1/company/job/candidate',
    'POST',
    {
      isAuth: true,
      query,
    }
  );
  return result;
};

export const fetchJobsForEntityMemberAPI = async (
  query: FetchJobsQueryI
): Promise<FetchJobsResponseI> => {
  const result = await apiCall<FetchJobsQueryI, FetchJobsResponseI>(
    '/backend/api/v1/company/job/entity-member',
    'POST',
    {
      isAuth: true,
      query,
    }
  );
  return result;
};

import type {
  JobCandidateFetchOneResultI,
  FetchApplicantsQueryI,
  FetchApplicantsResponseI,
} from './types';

export const fetchJobDetailAPI = async (
  jobId: string
): Promise<JobCandidateFetchOneResultI> => {
  const result = await apiCall<string, JobCandidateFetchOneResultI>(
    `/backend/api/v1/company/job/candidate/${jobId}`,
    'GET',
    { isAuth: true }
  );
  return result;
};

export const closeJobAPI = async (jobId: string): Promise<void> => {
  await apiCall<unknown, unknown>(
    `/backend/api/v1/company/job/core/${jobId}`,
    'PATCH',
    {
      isAuth: true,
      payload: { expiryDate: new Date().toISOString() },
    }
  );
};

export const fetchApplicantsForJobAPI = async (
  query: FetchApplicantsQueryI
): Promise<FetchApplicantsResponseI> => {
  const result = await apiCall<FetchApplicantsQueryI, FetchApplicantsResponseI>(
    '/backend/api/v1/company/job/application/entity-member',
    'GET',
    {
      isAuth: true,
      query,
    }
  );
  return result;
};
