'use client'
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import Link from 'next/link';
import { SearchInput, Tabs } from '@/components';
import useAuth from '@/hooks/useAuth';
import Filter from '@assets/svg/Filter';
import {
  fetchJobsForCandidateAPI,
  fetchJobsForEntityMemberAPI,
} from '@/networks/jobs';
import type {
  FetchJobsQueryI,
  FetchJobsResultItemI,
  JobStatusI,
} from '@/networks/jobs/types';

export type JobsPropsI = Record<string, never>;
export type JobsTabI = {
  id: 'active' | 'deleted' | 'expired';
  label: string;
};

export type JobItemI = {
  id: string;
  slug: string;
  title: string;
  company: string;
  postedAt: string;
  avatarUrl: string | null;
};

const statusFromTab = (tab: JobsTabI['id']): JobStatusI => {
  switch (tab) {
    case 'deleted':
      return 'DELETED';
    case 'expired':
      return 'EXPIRED';
    default:
      return 'ACTIVE';
  }
};

const formatDate = (iso: string): string => {
  try {
    const d = new Date(iso);
    return d.toLocaleDateString();
  } catch {
    return '';
  }
};

const Jobs: React.FC<JobsPropsI> = () => {
  const tabs: JobsTabI[] = [
    { id: 'active', label: 'Active' },
    { id: 'deleted', label: 'Deleted' },
    { id: 'expired', label: 'Expired' },
  ];
  const [activeTab, setActiveTab] = useState<JobsTabI['id']>('active');
  const [search, setSearch] = useState('');

  const [items, setItems] = useState<FetchJobsResultItemI[]>([]);
  const [nextCursorId, setNextCursorId] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const loadMoreRef = useRef<HTMLDivElement>(null);

  const isEntityProfile =
    typeof window !== 'undefined' &&
    localStorage.getItem('activeProfileType') === 'ENTITY';

  const entityProfileId =
    typeof window !== 'undefined'
      ? localStorage.getItem('entityProfileId')
      : null;

  const fetchJobs = useCallback(
    async (isLoadMore = false) => {
      try {
        if (isLoadMore) {
          if (!hasMore || !nextCursorId) return;
          setLoadingMore(true);
        } else {
          setLoading(true);
        }

        const baseQuery: FetchJobsQueryI = {
          cursorId: isLoadMore && nextCursorId ? String(nextCursorId) : null,
          pageSize: 10,
          status: statusFromTab(activeTab),
          isOfficial: isEntityProfile ? true : false,
          ...(isEntityProfile && entityProfileId
            ? { entity: { id: entityProfileId, dataType: 'master' } }
            : {}),
        };

        const apiFn = isEntityProfile
          ? fetchJobsForEntityMemberAPI
          : fetchJobsForCandidateAPI;

        const res = await apiFn(baseQuery);

        setItems(prev => (isLoadMore ? [...prev, ...res.data] : res.data));
        setNextCursorId(res.nextCursorId);
        setHasMore(!!res.nextCursorId && res.data.length > 0);
      } catch (e) {
        console.error('Failed to fetch jobs', e);
      } finally {
        setLoading(false);
        setLoadingMore(false);
      }
    },
    [activeTab, isEntityProfile, entityProfileId, nextCursorId, hasMore]
  );

  useEffect(() => {
    setItems([]);
    setNextCursorId(null);
    setHasMore(true);
    fetchJobs(false);
  }, [activeTab, isEntityProfile]);

  useEffect(() => {
    const el = loadMoreRef.current;
    if (!el) return;
    const observer = new IntersectionObserver(
      entries => {
        const [entry] = entries;
        if (entry.isIntersecting && hasMore && !loading && !loadingMore) {
          fetchJobs(true);
        }
      },
      { root: null, rootMargin: '100px', threshold: 0.1 }
    );
    observer.observe(el);
    return () => {
      observer.disconnect();
    };
  }, [fetchJobs, hasMore, loading, loadingMore]);

  const jobs: JobItemI[] = useMemo(() => {
    const mapped = items.map(j => ({
      id: j.id,
      slug: j.id,
      title: j.designation?.name || 'Job',
      company: j.entity?.name || '—',
      postedAt: j.createdAt ? formatDate(j.createdAt) : '',
      avatarUrl: null,
    }));

    const s = search.trim().toLowerCase();
    if (!s) return mapped;
    return mapped.filter(
      j =>
        j.title.toLowerCase().includes(s) || j.company.toLowerCase().includes(s)
    );
  }, [items, search]);

  const { isAuthenticated } = useAuth();

  return (
    <section className="lg:col-span-6 col-span-1">
      <div className="space-y-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-bold text-xl">
            {isEntityProfile ? 'Jobs' : 'My Jobs'}
          </h3>
          {isAuthenticated && (
            <Link
              className="text-primary font-medium text-base"
              href={'/jobs/add'}
            >
              {' '}
              Post new Job{' '}
            </Link>
          )}
        </div>
        <div className="bg-white border border-gray-200 rounded-2xl shadow-sm overflow-hidden">
          <div className="px-4 pt-4">
            <Tabs
              items={tabs}
              activeId={activeTab}
              onChange={id => setActiveTab(id as JobsTabI['id'])}
            />
          </div>

          <div className="px-4 py-3">
            <SearchInput
              value={search}
              onChange={e => setSearch(e.target.value)}
              rightSlot={<Filter />}
            />
          </div>

          {loading && items.length === 0 ? (
            <div className="px-4 py-12 text-center text-gray-500">
              Loading jobs…
            </div>
          ) : jobs.length === 0 ? (
            <div className="px-4 py-12 text-center text-gray-500">
              No results found
            </div>
          ) : (
            <ul className="divide-y divide-gray-200">
              {jobs.map(job => (
                <li key={`${activeTab}-${job.id}`} className="px-4">
                  <Link
                    href={`/jobs/${job.slug}`}
                    className="block hover:bg-gray-50 rounded-lg"
                  >
                    <div className="flex gap-4 py-4">
                      <div className="h-8 w-8 rounded-full overflow-hidden flex-shrink-0 bg-gray-100" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-semibold text-gray-900 truncate">
                          {job.title}
                        </p>
                        <p className="text-sm text-gray-700 truncate">
                          {job.company}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {job.postedAt}
                        </p>
                      </div>
                    </div>
                  </Link>
                </li>
              ))}
            </ul>
          )}

          <div ref={loadMoreRef} />
          {loadingMore && (
            <div className="px-4 py-4 text-sm text-gray-500">Loading more…</div>
          )}
        </div>
      </div>
    </section>
  );
};

export default Jobs;
