export type IdTypeI = { id: string; dataType: 'master' | 'raw' };

export type JobFormDataI = {
  // Step 1 UI-bound values
  entityId: string;
  designationId: string;
  departmentId: string;
  shipImo: string;
  shipType?: string;
  jobType: 'SAILING' | 'NON_SAILING' | '';
  locationCountryIso2: string;
  genderDiversityIndex: number; // 0..1
  expiryDate: string; // YYYY-MM-DD
  isOfficial: boolean;
  minYears: number;
  minSalary: number;
  maxYears?: number;
  maxSalary?: number;

  // Step 1 derived objects for backend
  entity?: IdTypeI;
  designation?: IdTypeI;
  department?: IdTypeI;
  ship?: { imo: string; dataType: 'master' | 'raw' };
  draftJobId?: string;

  // Application Type
  applicationType?: 'normal' | 'link' | 'email';
  applicationLink?: string;
  applicationEmail?: string;

  // Step 2
  about?: string;
  roles?: string;
  benefits?: string;
  // Certification rows
  certifications?: Array<{
    certification?: string;
    documentType?: string;
    countries?: string[] | never[];
    mandatory?: boolean;
  }>;
  documents?: Array<{
    documentType?: string;
    countries?: string[] | never[];
    mandatory?: boolean;
  }>;
  experiences?: Array<{
    designation?: string;
    shipType?: string;
    months?: number;
    mandatory?: boolean;
  }>;
  equipments?: Array<{
    type?: string; // equipment-category id
    make?: string; // equipment-manufacturer id
    model?: string; // equipment-model id
    fuelType?: boolean; // toggle
    moe?: number; // numeric input
    mandatory?: boolean;
  }>;
  cargos?: Array<{
    name?: string;
    code?: string;
    months?: number;
    mandatory?: boolean;
  }>;
  skills?: Array<{
    name?: string; // skill id
    mandatory?: boolean;
  }>;
  otherRequirements?: Array<{
    no?: number; // auto serial, display only
    details?: string;
    mandatory?: boolean;
  }>;

  // Step 3 - Benefits
  salaryType?: 'CONTRACTUAL' | 'ROUND_THE_YEAR' | 'OTHER';
  salaryCurrency?: string; // currency code
  salaryMin?: number;
  salaryMax?: number;
  salaryMandatory?: boolean;

  contractUnit?: 'months' | 'days';
  contractDuration?: number;

  internetAvailability?: 'YES' | 'NO';
  internetSpeed?: number; // Mbps
  internetLimitPerDay?: number; // MB per day
  internetDetails?: string;

  insuranceType?: 'SELF' | 'FAMILY' | 'OTHER';
  itfType?: 'ITF' | 'NON_ITF';
  familyOnboard?: 'YES' | 'NO';

  benefitItems?: Array<{
    no?: number;
    details?: string;
  }>;
};

export type BasicJobDataPayloadI = {
  entity: { id: string; dataType: 'master' | 'raw' };
  designation: { id: string; dataType: 'master' | 'raw' };
  department: { id: string; dataType: 'master' | 'raw' };
  ship?: { imo: string; dataType: 'master' | 'raw' };
  showShipDetails?: boolean;
  applicationMethod?: 'IN_APP' | 'EMAIL' | 'EXTERNAL_LINK';
  applicationEmail?: string;
  applicationUrl?: string;
  jobType?: 'SAILING' | 'NON_SAILING';
  countryIso2?: string;
  expiryDate: Date;
  joiningDate?: Date;
  genderDiversityIndex?: number;
  isOfficial: boolean;
  isUrgent?: boolean;
  minYears: number;
  minSalary: number;
  maxYears?: number;
  maxSalary?: number;
};

export type JobRequirementsPayloadI = {
  stage: '2';
  about?: string;
  rolesResponsibilities?: string;
  requirementType: 'BASIC' | 'ADVANCED';
  benefits?: string;

  // Advanced requirements fields
  certificationRequirements?: Array<{
    certification: IdTypeI;
    isMandatory: boolean;
  }>;
  documentRequirements?: Array<{
    documentType: IdTypeI;
    countries?: string[];
    isMandatory: boolean;
    description?: string;
  }>;
  experienceRequirements?: Array<{
    designation?: IdTypeI;
    shipType?: IdTypeI;
    monthsOfExperience: number;
    isMandatory: boolean;
    isTotal: boolean;
  }>;
  skillRequirements?: Array<{
    skill: IdTypeI;
    isMandatory: boolean;
  }>;
  cargoRequirements?: Array<{
    name: string;
    code?: string;
    monthsOfExperience: number;
    isMandatory: boolean;
  }>;
  otherRequirements?: Array<{
    details: string;
    isMandatory: boolean;
  }>;
};

export type JobBenefitsPayloadI = {
  stage: '3';
  benefitType?: 'BASIC' | 'ADVANCED';
  benefits?: string;
  salaryType?: 'CONTRACTUAL' | 'ROUND_THE_YEAR' | 'OTHER';
  currencyCode?: string;
  minSalary?: number;
  maxSalary?: number;
  showSalary?: boolean;
  contractMonths?: number;
  contractDays?: number;
  internetAvailable?: boolean;
  internetSpeed?: number;
  internetLimitPerDay?: number;
  internetDetails?: string;
  insuranceType?: 'SELF' | 'FAMILY' | 'OTHER';
  familyOnboard?: boolean;
  itfType?: 'ITF' | 'NON_ITF';
  benefitDetails?: Array<{ details: string }>;
};
