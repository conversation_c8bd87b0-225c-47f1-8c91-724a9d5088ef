import { Metadata } from 'next';
import { generateMetadata } from '@/lib/seo';
import { BreadcrumbStructuredData } from '@/components/StructuredData';

export const metadata: Metadata = generateMetadata({
  title: 'Maritime Jobs - Navicater | Find and Post Maritime Jobs',
  description:
    'Explore maritime job opportunities across the shipping industry. Apply for seafarer roles, marine engineering positions, and shore-based maritime careers.',
  keywords: [
    'maritime jobs',
    'seafarer jobs',
    'marine engineering jobs',
    'shipping jobs',
    'offshore jobs',
    'deck officer jobs',
    'engineer officer jobs',
    'shore jobs maritime',
    'navicater jobs',
  ],
  canonical: '/myjobs',
  openGraph: {
    title: 'Maritime Jobs - Find Your Next Role',
    description:
      'Discover and apply to jobs in the maritime industry. Connect with employers and advance your career.',
    type: 'website',
    url: '/jobs',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Maritime Jobs - Find Your Next Role',
    description:
      'Discover and apply to jobs in the maritime industry. Connect with employers and advance your career.',
  },
});

export default function MyJobsRouteLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const breadcrumbItems = [
    { name: 'Home', url: '/' },
    { name: 'Jobs', url: '/myjobs' },
  ];

  return (
    <>
      <BreadcrumbStructuredData items={breadcrumbItems} />
      {children}
    </>
  );
}
