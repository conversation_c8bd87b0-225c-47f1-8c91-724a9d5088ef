'use client';

import React from 'react';
import { ApiSelect } from '@/components';
import type { ApiSelectParamsI, ApiSelectResponseI } from '../ApiSelect/types';
import type { Control } from 'react-hook-form';
import { apiCall } from '@/lib/api';

const fetchDocumentTypes = async (
  params: ApiSelectParamsI
): Promise<ApiSelectResponseI> => {
  const query = {
    search:
      params.search && params.search.trim() !== '' ? params.search : undefined,
    page: Number(params.page) || 0,
    pageSize: 10,
  };
  const response = await apiCall(
    '/backend/api/v1/document/document-type/all/options',
    'GET',
    {
      isAuth: true,
      query,
    }
  );
  return response as ApiSelectResponseI;
};

export type DocumentsSelectPropsI = {
  control: Control<any>;
  name: string;
  label?: string;
  placeholder?: string;
  isRequired?: boolean;
  disabled?: boolean;
  className?: string;
  onChange?: (item: any) => void;
};

const DocumentsSelect = ({
  control,
  name,
  label = 'Document',
  placeholder = 'Select document',
  isRequired = false,
  disabled = false,
  className = 'w-full',
  onChange,
}: DocumentsSelectPropsI) => {
  return (
    <ApiSelect
      control={control}
      name={name}
      label={label}
      placeholder={placeholder}
      isRequired={isRequired}
      disabled={disabled}
      className={className}
      searchPlaceholder="Search documents..."
      apiCall={fetchDocumentTypes}
      onChangeOption={onChange}
      optionLabelKey="name"
      optionValueKey="id"
      uniqueKey="id"
      requiredMessage="Please select a document"
    />
  );
};

export default DocumentsSelect;
