'use client';

import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Controller } from 'react-hook-form';
import {
  ChevronDownIcon,
  CheckIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import useApiSelect from '../ApiSelect/useHook';
import type {
  ApiSelectParamsI,
  ApiSelectResponseI,
  ApiSelectOptionI,
} from '../ApiSelect/types';

export type MultipleSelectPropsI = {
  control: any;
  name: string;
  label?: string;
  placeholder?: string;
  isRequired?: boolean;
  disabled?: boolean;
  className?: string;
  searchPlaceholder?: string;
  onChangeOption?: (item: any, action: 'add' | 'remove') => void;
  apiCall: (params: ApiSelectParamsI) => Promise<ApiSelectResponseI>;
  optionLabelKey: string;
  optionValueKey: string;
  uniqueKey?: string; // For deduplication, defaults to optionValueKey
  requiredMessage?: string;
};

const MultipleSelect = ({
  control,
  name,
  label,
  placeholder = 'Select options',
  isRequired = false,
  disabled = false,
  className = 'w-full',
  searchPlaceholder = 'Search...',
  apiCall,
  optionLabelKey,
  optionValueKey,
  uniqueKey,
  requiredMessage,
  onChangeOption,
}: MultipleSelectPropsI) => {
  const { options, loading, loadMore, hasMore, onSearch } = useApiSelect({
    apiCall,
    uniqueKey: uniqueKey || optionValueKey,
  });

  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const listRef = useRef<HTMLDivElement>(null);

  const optionMap = useMemo(() => {
    const m = new Map<string, ApiSelectOptionI>();
    options?.forEach(opt => {
      const key = String(opt[optionValueKey]);
      m.set(key, opt);
    });
    return m;
  }, [options, optionValueKey]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen]);

  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
      const scrolledPercentage = (scrollTop + clientHeight) / scrollHeight;
      if (scrolledPercentage >= 0.8 && hasMore && !loading) {
        loadMore();
      }
    },
    [hasMore, loading, loadMore]
  );

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const term = e.target.value;
      setSearchTerm(term);
      onSearch(term);
    },
    [onSearch]
  );

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (disabled) return;

      switch (e.key) {
        case 'Enter':
        case ' ':
          e.preventDefault();
          setIsOpen(!isOpen);
          break;
        case 'Escape':
          setIsOpen(false);
          break;
        case 'ArrowDown':
          if (!isOpen) {
            e.preventDefault();
            setIsOpen(true);
          }
          break;
      }
    },
    [disabled, isOpen]
  );

  // Define toggleSelect at the top level with useCallback
  const toggleSelect = useCallback(
    (
      opt: ApiSelectOptionI,
      onChange: (value: string[]) => void,
      selectedIds: string[]
    ) => {
      const id = String(opt[optionValueKey]);
      let updated: string[];
      if (selectedIds.includes(id)) {
        updated = selectedIds.filter(v => v !== id);
        onChangeOption?.(opt, 'remove');
      } else {
        updated = [...selectedIds, id];
        onChangeOption?.(opt, 'add');
      }
      onChange(updated);
    },
    [optionValueKey, onChangeOption]
  );

  // Define removeChip at the top level with useCallback
  const removeChip = useCallback(
    (
      id: string,
      onChange: (value: string[]) => void,
      selectedIds: string[],
      optionMap: Map<string, ApiSelectOptionI>
    ) => {
      const opt = optionMap.get(id);
      const updated = selectedIds.filter(v => v !== id);
      onChange(updated);
      if (opt) onChangeOption?.(opt, 'remove');
    },
    [onChangeOption]
  );

  return (
    <Controller
      control={control}
      name={name}
      rules={{
        validate: value => {
          if (!isRequired) return true;
          const isValid = Array.isArray(value) && value.length > 0;
          return (
            isValid || requiredMessage || 'Please select at least one option'
          );
        },
      }}
      render={({ field: { onChange, value = [] }, fieldState: { error } }) => {
        const selectedIds: string[] = Array.isArray(value) ? value : [];

        return (
          <div className={`relative ${className}`}>
            {label && (
              <label
                htmlFor={`${name}-select`}
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                {label}
                {isRequired && <span className="text-red-500 ml-1">*</span>}
              </label>
            )}

            <div className="relative" ref={dropdownRef}>
              <button
                id={`${name}-select`}
                type="button"
                className={`
                  relative w-full cursor-default rounded-md border bg-white py-2 pl-3 pr-10 text-left shadow-sm 
                  focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary sm:text-sm
                  transition-colors duration-200
                  ${error ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'border-gray-300'}
                  ${disabled ? 'bg-gray-50 text-gray-500 cursor-not-allowed' : 'hover:border-gray-400'}
                `}
                aria-haspopup="listbox"
                aria-expanded={isOpen}
                aria-invalid={!!error}
                onClick={() => !disabled && setIsOpen(!isOpen)}
                onKeyDown={handleKeyDown}
                disabled={disabled}
              >
                <span className="flex flex-wrap gap-1 min-h-[1.25rem]">
                  {selectedIds.length === 0 ? (
                    <span className="text-gray-500 leading-5">
                      {placeholder}
                    </span>
                  ) : (
                    selectedIds.map(id => {
                      const option = optionMap.get(id);
                      const label = option?.[optionLabelKey] ?? id;

                      return (
                        <span
                          key={id}
                          className="inline-flex items-center gap-1 bg-green-100 text-primary px-2 py-0.5 rounded-full text-xs font-medium"
                        >
                          <span
                            className="max-w-[150px] truncate"
                            title={String(label)}
                          >
                            {String(label)}
                          </span>
                          <button
                            type="button"
                            className="ml-1 text-green-600 hover:text-green-800 focus:outline-none focus:text-primary transition-colors"
                            onClick={e => {
                              e.stopPropagation();
                              removeChip(id, onChange, selectedIds, optionMap);
                            }}
                            aria-label={`Remove ${label}`}
                            tabIndex={-1}
                          >
                            <XMarkIcon className="w-3 h-3" />
                          </button>
                        </span>
                      );
                    })
                  )}
                </span>

                <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                  <ChevronDownIcon
                    className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${
                      isOpen ? 'rotate-180' : ''
                    }`}
                    aria-hidden="true"
                  />
                </span>
              </button>

              {isOpen && (
                <div className="absolute z-50 mt-1 w-full rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                  <div className="px-3 py-2 border-b border-gray-200">
                    <input
                      type="text"
                      placeholder={searchPlaceholder}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:primary transition-colors"
                      value={searchTerm}
                      onChange={handleSearchChange}
                      autoFocus
                    />
                  </div>

                  <div
                    ref={listRef}
                    className="max-h-60 overflow-auto"
                    onScroll={handleScroll}
                    role="listbox"
                    aria-multiselectable="true"
                  >
                    {options && options.length > 0 ? (
                      options.map(opt => {
                        const id = String(opt[optionValueKey]);
                        const isSelected = selectedIds.includes(id);
                        const label = String(opt[optionLabelKey]);

                        return (
                          <div
                            key={id}
                            role="option"
                            aria-selected={isSelected}
                            className={`relative cursor-pointer select-none py-2 pl-3 pr-9 transition-colors ${
                              isSelected
                                ? 'bg-primary text-white'
                                : 'text-gray-900 hover:bg-green-50'
                            }`}
                            onClick={() =>
                              toggleSelect(opt, onChange, selectedIds)
                            }
                          >
                            <span
                              className={`block truncate ${isSelected ? 'font-medium' : 'font-normal'}`}
                            >
                              {label}
                            </span>
                            {isSelected && (
                              <span className="absolute inset-y-0 right-0 flex items-center pr-4">
                                <CheckIcon
                                  className="h-5 w-5"
                                  aria-hidden="true"
                                />
                              </span>
                            )}
                          </div>
                        );
                      })
                    ) : !loading ? (
                      <div className="px-3 py-8 text-center text-gray-500 text-sm">
                        No options found
                      </div>
                    ) : null}

                    {loading && (
                      <div className="flex justify-center items-center p-4 text-sm text-gray-500">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                        <span className="ml-2">Loading...</span>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {error && (
              <p className="mt-1 text-sm text-red-600" role="alert">
                {String(error.message)}
              </p>
            )}
          </div>
        );
      }}
    />
  );
};

export default MultipleSelect;
