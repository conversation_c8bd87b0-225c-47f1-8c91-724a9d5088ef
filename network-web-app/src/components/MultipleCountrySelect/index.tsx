'use client';

import React from 'react';
import { Control } from 'react-hook-form';
import MultipleSelect from '../MultipleSelect';
import { fetchCountries } from '@/networks/data/country';

export type MultipleCountrySelectPropsI = {
  control: Control<any>;
  name: string;
  label?: string;
  placeholder?: string;
  isRequired?: boolean;
  disabled?: boolean;
  className?: string;
};

const MultipleCountrySelect = ({
  control,
  name,
  label = 'Countries',
  placeholder = 'Select countries',
  isRequired = false,
  disabled = false,
  className = 'w-full',
}: MultipleCountrySelectPropsI) => {
  return (
    <MultipleSelect
      control={control}
      name={name}
      label={label}
      placeholder={placeholder}
      isRequired={isRequired}
      disabled={disabled}
      className={className}
      searchPlaceholder="Search countries..."
      apiCall={fetchCountries as any}
      optionLabelKey="name"
      optionValueKey="iso2"
      uniqueKey="iso2"
      requiredMessage="Please select at least one country"
    />
  );
};

export default MultipleCountrySelect;
